dbType = "postgres"
dbName = "b2b_oms"
tableName = "consignment_container_status_log"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.b2b_oms.public.consignment_container_status_log"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "b2b_oms.consignment_container_status_log"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "b2b_oms/consignment_container_status_log/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/b2b_oms/consignment_container_status_log/v1"
  }
}
hive {
  table = "consignment_container_status_log"
  database = "lake_b2b_oms"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
