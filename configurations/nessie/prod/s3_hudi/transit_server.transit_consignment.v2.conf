dbType = "postgres"
dbName = "transit_server"
tableName = "transit_consignment"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.transit_server.public.transit_consignment"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "install_ts"
    name = "transit_server.transit_consignment"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "transit_server/transit_consignment/v2"
    fullPath = "s3://prod-data-lake-hudi-applications/transit_server/transit_consignment/v2"
  }
}
hive {
  table = "transit_consignment"
  database = "lake_transit_server"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
