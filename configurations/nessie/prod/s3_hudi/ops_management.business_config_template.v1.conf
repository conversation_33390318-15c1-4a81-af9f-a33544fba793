dbType = "postgres"
dbName = "ops_management"
tableName = "business_config_template"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.ops_management.public.business_config_template"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "install_ts"
    name = "ops_management.business_config_template"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "ops_management/business_config_template/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/ops_management/business_config_template/v1"
  }
}
hive {
  table = "business_config_template"
  database = "lake_ops_management"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
