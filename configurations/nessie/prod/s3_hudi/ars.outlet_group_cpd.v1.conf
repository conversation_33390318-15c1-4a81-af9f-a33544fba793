dbType = "mysql"
dbName = "ars"
tableName = "outlet_group_cpd"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.ars.ars.outlet_group_cpd"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "ars.outlet_group_cpd"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "ars/outlet_group_cpd/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/ars/outlet_group_cpd/v1"
  }
}
hive {
  table = "outlet_group_cpd"
  database = "lake_ars"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
