dbType = "postgres"
dbName = "transit_server"
tableName = "transit_courier_order"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.transit_server.public.transit_courier_order"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "install_ts"
    name = "transit_server.transit_courier_order"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "transit_server/transit_courier_order/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/transit_server/transit_courier_order/v1"
  }
}
hive {
  table = "transit_courier_order"
  database = "lake_transit_server"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
