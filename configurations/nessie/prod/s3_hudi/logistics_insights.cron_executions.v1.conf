dbType = "postgres"
dbName = "logistics_insights"
tableName = "cron_executions"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.logistics_insights.public.cron_executions"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "logistics_insights.cron_executions"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "logistics_insights/cron_executions/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/logistics_insights/cron_executions/v1"
  }
}
hive {
  table = "cron_executions"
  database = "lake_logistics_insights"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
