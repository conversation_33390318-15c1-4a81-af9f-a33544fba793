dbType = "postgres"
dbName = "airflow"
tableName = "dag_run"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.airflow.public.dag_run"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "execution_date"
    name = "airflow.dag_run"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "airflow/dag_run/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/airflow/dag_run/v1"
  }
}
hive {
  table = "dag_run"
  database = "lake_airflow"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
