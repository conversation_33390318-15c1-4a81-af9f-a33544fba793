dbType = "mysql"
dbName = "ars"
tableName = "ars_job_run"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.ars.ars.ars_job_run"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "run_id"
    name = "ars.ars_job_run"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "ars/ars_job_run/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/ars/ars_job_run/v1"
  }
}
hive {
  table = "ars_job_run"
  database = "lake_ars"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
