dbType = "mysql"
dbName = "wms"
tableName = "app_layout"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.wms.wms.app_layout"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "wms.app_layout"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "wms/app_layout/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/wms/app_layout/v1"
  }
}
hive {
  table = "app_layout"
  database = "lake_wms"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
