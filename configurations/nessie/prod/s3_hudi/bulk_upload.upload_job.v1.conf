dbType = "mysql"
dbName = "bulk_upload"
tableName = "upload_job"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.bulk_upload.bulk_upload.upload_job"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "bulk_upload.upload_job"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "bulk_upload/upload_job/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/bulk_upload/upload_job/v1"
  }
}
hive {
  table = "upload_job"
  database = "lake_bulk_upload"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
