dbType = "postgres"
dbName = "seller"
tableName = "campaigns"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.seller.public.campaigns"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "seller.campaigns"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "seller/campaigns/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/seller/campaigns/v1"
  }
}
hive {
  table = "campaigns"
  database = "lake_seller"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
