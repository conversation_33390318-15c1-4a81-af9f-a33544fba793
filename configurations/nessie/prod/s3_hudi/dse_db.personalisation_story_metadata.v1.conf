dbType = "postgres"
dbName = "dse_db"
tableName = "personalisation_story_metadata"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.dse_db.public.personalisation_story_metadata"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "dse_db.personalisation_story_metadata"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "dse_db/personalisation_story_metadata/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/dse_db/personalisation_story_metadata/v1"
  }
}
hive {
  table = "personalisation_story_metadata"
  database = "lake_dse_db"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
