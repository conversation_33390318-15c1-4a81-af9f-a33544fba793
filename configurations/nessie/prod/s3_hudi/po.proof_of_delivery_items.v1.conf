dbType = "mysql"
dbName = "po"
tableName = "proof_of_delivery_items"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.po.po.proof_of_delivery_items"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "po.proof_of_delivery_items"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "po/proof_of_delivery_items/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/po/proof_of_delivery_items/v1"
  }
}
hive {
  table = "proof_of_delivery_items"
  database = "lake_po"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
