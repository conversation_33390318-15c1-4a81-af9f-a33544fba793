dbType = "mysql"
dbName = "po"
tableName = "invoice_dn_details"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.po.po.invoice_dn_details"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "po.invoice_dn_details"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "po/invoice_dn_details/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/po/invoice_dn_details/v1"
  }
}
hive {
  table = "invoice_dn_details"
  database = "lake_po"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake" 