dbType = "postgres"
dbName = "locus_v2"
tableName = "sub_forms"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.locus_v2.public.sub_forms"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "locus_v2.sub_forms"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "locus_v2/sub_forms/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/locus_v2/sub_forms/v1"
  }
}
hive {
  table = "sub_forms"
  database = "lake_locus_v2"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
