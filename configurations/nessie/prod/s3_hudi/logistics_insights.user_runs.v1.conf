dbType = "postgres"
dbName = "logistics_insights"
tableName = "user_runs"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.logistics_insights.public.user_runs"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "logistics_insights.user_runs"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "logistics_insights/user_runs/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/logistics_insights/user_runs/v1"
  }
}
hive {
  table = "user_runs"
  database = "lake_logistics_insights"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
