dbType = "postgres"
dbName = "pricing_engine"
tableName = "offer_product_id_constituents"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.pricing_engine.public.offer_product_id_constituents"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "pricing_engine.offer_product_id_constituents"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "pricing_engine/offer_product_id_constituents/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/pricing_engine/offer_product_id_constituents/v1"
  }
}
hive {
  table = "offer_product_id_constituents"
  database = "lake_pricing_engine"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
