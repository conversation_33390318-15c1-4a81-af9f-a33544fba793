dbType = "postgres"
dbName = "ops_management"
tableName = "selfie_verification_attempts"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.ops_management.public.selfie_verification_attempts"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "install_ts"
    name = "ops_management.selfie_verification_attempts"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "ops_management/selfie_verification_attempts/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/ops_management/selfie_verification_attempts/v1"
  }
}
hive {
  table = "selfie_verification_attempts"
  database = "lake_ops_management"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
