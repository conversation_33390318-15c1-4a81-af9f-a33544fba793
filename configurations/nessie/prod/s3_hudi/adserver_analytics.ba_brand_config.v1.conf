dbType = "postgres"
dbName = "adserver_analytics"
tableName = "ba_brand_config"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.adserver_analytics.public.ba_brand_config"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "category_id,brand_id,config_type"
    partitionKey = "start_date"
    name = "adserver_analytics.ba_brand_config"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "adserver_analytics/ba_brand_config/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/adserver_analytics/ba_brand_config/v1"
  }
}
hive {
  table = "ba_brand_config"
  database = "lake_adserver_analytics"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
