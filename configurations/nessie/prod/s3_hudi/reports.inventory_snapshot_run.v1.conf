dbType = "mysql"
dbName = "reports"
tableName = "inventory_snapshot_run"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.reports.reports.inventory_snapshot_run"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "snapshot_datetime"
    name = "reports.inventory_snapshot_run"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "reports/inventory_snapshot_run/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/reports/inventory_snapshot_run/v1"
  }
}
hive {
  table = "inventory_snapshot_run"
  database = "lake_reports"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
