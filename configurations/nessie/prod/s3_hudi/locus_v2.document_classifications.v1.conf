dbType = "postgres"
dbName = "locus_v2"
tableName = "document_classifications"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.locus_v2.public.document_classifications"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "locus_v2.document_classifications"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "locus_v2/document_classifications/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/locus_v2/document_classifications/v1"
  }
}
hive {
  table = "document_classifications"
  database = "lake_locus_v2"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
