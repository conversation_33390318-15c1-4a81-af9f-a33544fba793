dbType = "mysql"
dbName = "ars"
tableName = "b2b_transfer_rule_attributes"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.ars.ars.b2b_transfer_rule_attributes"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "ars.b2b_transfer_rule_attributes"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "ars/b2b_transfer_rule_attributes/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/ars/b2b_transfer_rule_attributes/v1"
  }
}
hive {
  table = "b2b_transfer_rule_attributes"
  database = "lake_ars"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
