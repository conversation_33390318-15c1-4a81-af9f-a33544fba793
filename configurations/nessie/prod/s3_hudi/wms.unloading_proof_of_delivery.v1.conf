dbType = "mysql"
dbName = "wms"
tableName = "unloading_proof_of_delivery"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.wms.wms.unloading_proof_of_delivery"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "wms.unloading_proof_of_delivery"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "wms/unloading_proof_of_delivery/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/wms/unloading_proof_of_delivery/v1"
  }
}
hive {
  table = "unloading_proof_of_delivery"
  database = "lake_wms"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
