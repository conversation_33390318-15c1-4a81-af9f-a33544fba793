dbType = "mysql"
dbName = "rpc"
tableName = "facility_adjacency"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.rpc.rpc.facility_adjacency"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "rpc.facility_adjacency"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "rpc/facility_adjacency/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/rpc/facility_adjacency/v1"
  }
}
hive {
  table = "facility_adjacency"
  database = "lake_rpc"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
