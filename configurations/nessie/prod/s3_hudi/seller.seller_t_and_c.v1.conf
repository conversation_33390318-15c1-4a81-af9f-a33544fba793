dbType = "postgres"
dbName = "seller"
tableName = "seller_t_and_c"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.seller.public.seller_t_and_c"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "install_ts"
    name = "seller.seller_t_and_c"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "seller/seller_t_and_c/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/seller/seller_t_and_c/v1"
  }
}
hive {
  table = "seller_t_and_c"
  database = "lake_seller"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
