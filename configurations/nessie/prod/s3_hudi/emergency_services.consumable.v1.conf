dbType = "postgres"
dbName = "emergency_services"
tableName = "consumable"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.emergency_services.public.consumable"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "emergency_services.consumable"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "emergency_services/consumable/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/emergency_services/consumable/v1"
  }
}
hive {
  table = "consumable"
  database = "lake_emergency_services"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
