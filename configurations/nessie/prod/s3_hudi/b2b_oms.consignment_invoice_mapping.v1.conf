dbType = "postgres"
dbName = "b2b_oms"
tableName = "consignment_invoice_mapping"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.b2b_oms.public.consignment_invoice_mapping"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "b2b_oms.consignment_invoice_mapping"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "b2b_oms/consignment_invoice_mapping/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/b2b_oms/consignment_invoice_mapping/v1"
  }
}
hive {
  table = "consignment_invoice_mapping"
  database = "lake_b2b_oms"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
