dbType = "postgres"
dbName = "pricing_engine"
tableName = "sheet_files"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.pricing_engine.public.sheet_files"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "pricing_engine.sheet_files"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "pricing_engine/sheet_files/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/pricing_engine/sheet_files/v1"
  }
}
hive {
  table = "sheet_files"
  database = "lake_pricing_engine"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
