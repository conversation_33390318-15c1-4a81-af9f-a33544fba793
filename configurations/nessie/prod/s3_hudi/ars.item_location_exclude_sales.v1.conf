dbType = "mysql"
dbName = "ars"
tableName = "item_location_exclude_sales"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.ars.ars.item_location_exclude_sales"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "ars.item_location_exclude_sales"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "ars/item_location_exclude_sales/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/ars/item_location_exclude_sales/v1"
  }
}
hive {
  table = "item_location_exclude_sales"
  database = "lake_ars"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
