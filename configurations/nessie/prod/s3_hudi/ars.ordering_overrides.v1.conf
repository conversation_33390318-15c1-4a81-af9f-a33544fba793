dbType = "mysql"
dbName = "ars"
tableName = "ordering_overrides"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.ars.ars.ordering_overrides"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "override_date"
    name = "ars.ordering_overrides"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "ars/ordering_overrides/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/ars/ordering_overrides/v1"
  }
}
hive {
  table = "ordering_overrides"
  database = "lake_ars"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
