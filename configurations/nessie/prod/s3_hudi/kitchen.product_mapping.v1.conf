dbType = "postgres"
dbName = "kitchen"
tableName = "product_mapping"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.kitchen.public.product_mapping"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "kitchen.product_mapping"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "kitchen/product_mapping/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/kitchen/product_mapping/v1"
  }
}
hive {
  table = "product_mapping"
  database = "lake_kitchen"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
