dbType = "postgres"
dbName = "seller"
tableName = "seller_product_request_mappings"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.seller.public.seller_product_request_mappings"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "seller.seller_product_request_mappings"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "seller/seller_product_request_mappings/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/seller/seller_product_request_mappings/v1"
  }
}
hive {
  table = "seller_product_request_mappings"
  database = "lake_seller"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
