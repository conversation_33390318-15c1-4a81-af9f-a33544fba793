dbType = "postgres"
dbName = "emergency_services"
tableName = "patient_details"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.emergency_services.public.patient_details"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "emergency_services.patient_details"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "emergency_services/patient_details/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/emergency_services/patient_details/v1"
  }
}
hive {
  table = "patient_details"
  database = "lake_emergency_services"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
