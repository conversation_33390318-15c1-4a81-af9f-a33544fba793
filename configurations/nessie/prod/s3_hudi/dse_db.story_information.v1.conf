dbType = "postgres"
dbName = "dse_db"
tableName = "story_information"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.dse_db.public.story_information"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "dse_db.story_information"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "dse_db/story_information/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/dse_db/story_information/v1"
  }
}
hive {
  table = "story_information"
  database = "lake_dse_db"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
