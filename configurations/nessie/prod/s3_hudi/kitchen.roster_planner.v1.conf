dbType = "postgres"
dbName = "kitchen"
tableName = "roster_planner"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.kitchen.public.roster_planner"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "kitchen.roster_planner"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "kitchen/roster_planner/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/kitchen/roster_planner/v1"
  }
}
hive {
  table = "roster_planner"
  database = "lake_kitchen"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
