dbType = "postgres"
dbName = "b2b_oms"
tableName = "consignment"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.b2b_oms.public.consignment"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "b2b_oms.consignment"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "b2b_oms/consignment/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/b2b_oms/consignment/v1"
  }
}
hive {
  table = "consignment"
  database = "lake_b2b_oms"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
