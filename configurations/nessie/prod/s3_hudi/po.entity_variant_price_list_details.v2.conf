dbType = "mysql"
dbName = "po"
tableName = "entity_variant_price_list_details"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.po.po.entity_variant_price_list_details"
  maxBatchRead = 200
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "po.entity_variant_price_list_details"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "po/entity_variant_price_list_details/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/po/entity_variant_price_list_details/v1"
  }
}
hive {
  table = "entity_variant_price_list_details"
  database = "lake_po"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
