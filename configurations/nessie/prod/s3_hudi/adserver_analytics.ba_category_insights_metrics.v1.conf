dbType = "postgres"
dbName = "adserver_analytics"
tableName = "ba_category_insights_metrics"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.adserver_analytics.public.ba_category_insights_metrics"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "category_id,metric,city_name,metric_type"
    partitionKey = "start_date"
    name = "adserver_analytics.ba_category_insights_metrics"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "adserver_analytics/ba_category_insights_metrics/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/adserver_analytics/ba_category_insights_metrics/v1"
  }
}
hive {
  table = "ba_category_insights_metrics"
  database = "lake_adserver_analytics"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
