dbType = "postgres"
dbName = "logistics_insights"
tableName = "input_groups"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.logistics_insights.public.input_groups"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "logistics_insights.input_groups"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "logistics_insights/input_groups/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/logistics_insights/input_groups/v1"
  }
}
hive {
  table = "input_groups"
  database = "lake_logistics_insights"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
