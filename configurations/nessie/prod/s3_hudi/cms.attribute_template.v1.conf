dbType = "postgres"
dbName = "cms"
tableName = "attribute_template"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.cms.public.attribute_template"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "cms.attribute_template"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "cms/attribute_template/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/cms/attribute_template/v1"
  }
}
hive {
  table = "attribute_template"
  database = "lake_cms"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
