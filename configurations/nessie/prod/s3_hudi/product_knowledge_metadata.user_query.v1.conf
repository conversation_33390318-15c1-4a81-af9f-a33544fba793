dbType = "postgres"
dbName = "product_knowledge_metadata"
tableName = "user_query"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.product_knowledge_metadata.public.user_query"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "product_knowledge_metadata.user_query"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "product_knowledge_metadata/user_query/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/product_knowledge_metadata/user_query/v1"
  }
}
hive {
  table = "user_query"
  database = "lake_product_knowledge_metadata"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
