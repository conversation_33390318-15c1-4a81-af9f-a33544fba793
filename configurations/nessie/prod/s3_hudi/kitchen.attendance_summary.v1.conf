dbType = "postgres"
dbName = "kitchen"
tableName = "attendance_summary"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.kitchen.public.attendance_summary"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "kitchen.attendance_summary"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "kitchen/attendance_summary/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/kitchen/attendance_summary/v1"
  }
}
hive {
  table = "attendance_summary"
  database = "lake_kitchen"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
