dbType = "mysql"
dbName = "wms"
tableName = "unloading_task_po_invoice_mapping"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.wms.wms.unloading_task_po_invoice_mapping"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "wms.unloading_task_po_invoice_mapping"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "wms/unloading_task_po_invoice_mapping/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/wms/unloading_task_po_invoice_mapping/v1"
  }
}
hive {
  table = "unloading_task_po_invoice_mapping"
  database = "lake_wms"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
