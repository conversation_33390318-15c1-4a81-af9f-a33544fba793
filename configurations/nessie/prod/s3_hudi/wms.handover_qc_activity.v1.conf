dbType = "mysql"
dbName = "wms"
tableName = "handover_qc_activity"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.wms.wms.handover_qc_activity"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "wms.handover_qc_activity"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "wms/handover_qc_activity/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/wms/handover_qc_activity/v1"
  }
}
hive {
  table = "handover_qc_activity"
  database = "lake_wms"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
