dbType = "mysql"
dbName = "rpc"
tableName = "facility_adjacency_log"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.rpc.rpc.facility_adjacency_log"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "rpc.facility_adjacency_log"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "rpc/facility_adjacency_log/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/rpc/facility_adjacency_log/v1"
  }
}
hive {
  table = "facility_adjacency_log"
  database = "lake_rpc"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
