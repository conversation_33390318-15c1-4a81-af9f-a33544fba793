dbType = "postgres"
dbName = "seller"
tableName = "seller_document_info"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.seller.public.seller_document_info"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "seller.seller_document_info"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "seller/seller_document_info/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/seller/seller_document_info/v1"
  }
}
hive {
  table = "seller_document_info"
  database = "lake_seller"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
