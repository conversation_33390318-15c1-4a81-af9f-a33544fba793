dbType = "postgres"
dbName = "cms"
tableName = "attribute_template_constituent_attribute_mapping"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.cms.public.attribute_template_constituent_attribute_mapping"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "cms.attribute_template_constituent_attribute_mapping"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "cms/attribute_template_constituent_attribute_mapping/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/cms/attribute_template_constituent_attribute_mapping/v1"
  }
}
hive {
  table = "attribute_template_constituent_attribute_mapping"
  database = "lake_cms"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
