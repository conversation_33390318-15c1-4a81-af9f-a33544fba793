dbType = "postgres"
dbName = "serviceability"
tableName = "ser_store_polygon_logs"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.serviceability.public.ser_store_polygon_logs"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "install_ts"
    name = "serviceability.ser_store_polygon_logs"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "serviceability/ser_store_polygon_logs/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/serviceability/ser_store_polygon_logs/v1"
  }
}
hive {
  table = "ser_store_polygon_logs"
  database = "lake_serviceability"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
