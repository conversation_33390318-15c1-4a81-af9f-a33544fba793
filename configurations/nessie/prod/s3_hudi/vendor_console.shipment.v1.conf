dbType = "postgres"
dbName = "vendor_console"
tableName = "shipment"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.vendor_console.public.shipment"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "vendor_console.shipment"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "vendor_console/shipment/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/vendor_console/shipment/v1"
  }
}
hive {
  table = "shipment"
  database = "lake_vendor_console"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
