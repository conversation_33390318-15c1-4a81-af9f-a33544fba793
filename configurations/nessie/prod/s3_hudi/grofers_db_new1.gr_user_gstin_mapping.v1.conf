dbType = "postgres"
dbName = "grofers_db_new1"
tableName = "gr_user_gstin_mapping"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.grofers_db_new1.public.gr_user_gstin_mapping"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "grofers_db_new1.gr_user_gstin_mapping"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "grofers_db_new1/gr_user_gstin_mapping/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/grofers_db_new1/gr_user_gstin_mapping/v1"
  }
}
hive {
  table = "gr_user_gstin_mapping"
  database = "lake_grofers_db"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
