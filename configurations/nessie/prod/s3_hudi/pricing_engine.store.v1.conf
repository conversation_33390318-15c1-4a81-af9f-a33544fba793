dbType = "postgres"
dbName = "pricing_engine"
tableName = "store"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.pricing_engine.public.store"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "pricing_engine.store"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "pricing_engine/store/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/pricing_engine/store/v1"
  }
}
hive {
  table = "store"
  database = "lake_pricing_engine"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
