dbType = "postgres"
dbName = "elite_store_partner"
tableName = "store"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.elite_store_partner.public.store"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "elite_store_partner.store"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "elite_store_partner/store/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/elite_store_partner/store/v1"
  }
}
hive {
  table = "store"
  database = "lake_elite_store_partner"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
