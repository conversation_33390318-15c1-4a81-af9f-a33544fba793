dbType = "mysql"
dbName = "wms"
tableName = "inbound_job_request"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.wms.wms.inbound_job_request"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "wms.inbound_job_request"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "wms/inbound_job_request/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/wms/inbound_job_request/v1"
  }
}
hive {
  table = "inbound_job_request"
  database = "lake_wms"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
