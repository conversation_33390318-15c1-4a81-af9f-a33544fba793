dbType = "postgres"
dbName = "pricing_engine"
tableName = "master_rule"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.pricing_engine.public.master_rule"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "pricing_engine.master_rule"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "pricing_engine/master_rule/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/pricing_engine/master_rule/v1"
  }
}
hive {
  table = "master_rule"
  database = "lake_pricing_engine"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
