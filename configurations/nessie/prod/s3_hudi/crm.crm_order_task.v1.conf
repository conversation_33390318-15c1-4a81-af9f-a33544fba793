dbType = "postgres"
dbName = "crm"
tableName = "crm_order_task"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.crm.public.crm_order_task"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "install_ts"
    name = "crm.crm_order_task"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "crm/crm_order_task/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/crm/crm_order_task/v1"
  }
}
hive {
  table = "crm_order_task"
  database = "lake_crm"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
