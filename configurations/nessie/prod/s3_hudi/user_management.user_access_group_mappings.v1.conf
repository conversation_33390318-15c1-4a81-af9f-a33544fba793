dbType = "postgres"
dbName = "user_management"
tableName = "user_access_group_mappings"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.user_management.public.user_access_group_mappings"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "user_management.user_access_group_mappings"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "user_management/user_access_group_mappings/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/user_management/user_access_group_mappings/v1"
  }
}
hive {
  table = "user_access_group_mappings"
  database = "lake_user_management"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
