dbType = "postgres"
dbName = "vendor_console"
tableName = "reservation_slot_details_log"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.vendor_console.public.reservation_slot_details_log"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "vendor_console.reservation_slot_details_log"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "vendor_console/reservation_slot_details_log/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/vendor_console/reservation_slot_details_log/v1"
  }
}
hive {
  table = "reservation_slot_details_log"
  database = "lake_vendor_console"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
