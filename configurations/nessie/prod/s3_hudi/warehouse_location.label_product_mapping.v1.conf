dbType = "mysql"
dbName = "warehouse_location"
tableName = "label_product_mapping"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.warehouse_location.warehouse_location.label_product_mapping"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "warehouse_location.label_product_mapping"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "warehouse_location/label_product_mapping/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/warehouse_location/label_product_mapping/v1"
  }
}
hive {
  table = "label_product_mapping"
  database = "lake_warehouse_location"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
