dbType = "postgres"
dbName = "ops_management"
tableName = "applied_business_config"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.ops_management.public.applied_business_config"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "install_ts"
    name = "ops_management.applied_business_config"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "ops_management/applied_business_config/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/ops_management/applied_business_config/v1"
  }
}
hive {
  table = "applied_business_config"
  database = "lake_ops_management"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
