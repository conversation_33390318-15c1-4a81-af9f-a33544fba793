dbType = "postgres"
dbName = "inventory_lm"
tableName = "legend_mapping_detail"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.inventory_lm.public.legend_mapping_detail"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "inventory_lm.legend_mapping_detail"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "inventory_lm/legend_mapping_detail/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/inventory_lm/legend_mapping_detail/v1"
  }
}
hive {
  table = "legend_mapping_detail"
  database = "lake_inventory_lm"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
