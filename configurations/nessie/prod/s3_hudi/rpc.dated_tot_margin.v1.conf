dbType = "mysql"
dbName = "rpc"
tableName = "dated_tot_margin"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.rpc.rpc.dated_tot_margin"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "rpc.dated_tot_margin"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "rpc/dated_tot_margin/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/rpc/dated_tot_margin/v1"
  }
}
hive {
  table = "dated_tot_margin"
  database = "lake_rpc"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
