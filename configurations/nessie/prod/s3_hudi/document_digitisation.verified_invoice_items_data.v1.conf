dbType = "postgres"
dbName = "document_digitisation"
tableName = "verified_invoice_items_data"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.document_digitisation.public.verified_invoice_items_data"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "document_digitisation.verified_invoice_items_data"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "document_digitisation/verified_invoice_items_data/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/document_digitisation/verified_invoice_items_data/v1"
  }
}
hive {
  table = "verified_invoice_items_data"
  database = "lake_document_digitisation"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
