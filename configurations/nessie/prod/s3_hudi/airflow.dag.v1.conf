dbType = "postgres"
dbName = "airflow"
tableName = "dag"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.airflow.public.dag"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "dag_id"
    name = "airflow.dag"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "airflow/dag/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/airflow/dag/v1"
  }
}
hive {
  table = "dag"
  database = "lake_airflow"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
