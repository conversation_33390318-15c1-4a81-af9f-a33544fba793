dbType = "postgres"
dbName = "logistics_insights"
tableName = "base_model_csvs"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.logistics_insights.public.base_model_csvs"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "logistics_insights.base_model_csvs"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "logistics_insights/base_model_csvs/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/logistics_insights/base_model_csvs/v1"
  }
}
hive {
  table = "base_model_csvs"
  database = "lake_logistics_insights"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
