dbType = "postgres"
dbName = "seller"
tableName = "scheduled_jobs"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.seller.public.scheduled_jobs"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "seller.scheduled_jobs"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "seller/scheduled_jobs/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/seller/scheduled_jobs/v1"
  }
}
hive {
  table = "scheduled_jobs"
  database = "lake_seller"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
