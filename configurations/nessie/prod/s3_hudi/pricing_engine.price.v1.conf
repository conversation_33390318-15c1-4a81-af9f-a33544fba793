dbType = "postgres"
dbName = "pricing_engine"
tableName = "price"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.pricing_engine.public.price"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "pricing_engine.price"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "pricing_engine/price/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/pricing_engine/price/v1"
  }
}
hive {
  table = "price"
  database = "lake_pricing_engine"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
