dbType = "postgres"
dbName = "search_management"
tableName = "suggestion"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.search_management.public.suggestion"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "search_management.suggestion"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "search_management/suggestion/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/search_management/suggestion/v1"
  }
}
hive {
  table = "suggestion"
  database = "lake_search_management"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
