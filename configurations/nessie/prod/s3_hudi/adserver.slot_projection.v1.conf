dbType = "postgres"
dbName = "adserver"
tableName = "slot_projection"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.adserver.public.slot_projection"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_ts"
    name = "adserver.slot_projection"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "adserver/slot_projection/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/adserver/slot_projection/v1"
  }
}
hive {
  table = "slot_projection"
  database = "lake_adserver"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
