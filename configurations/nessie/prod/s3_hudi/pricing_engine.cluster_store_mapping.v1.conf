dbType = "postgres"
dbName = "pricing_engine"
tableName = "cluster_store_mapping"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.pricing_engine.public.cluster_store_mapping"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "cluster_id,store_id"
    name = "pricing_engine.cluster_store_mapping"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "pricing_engine/cluster_store_mapping/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/pricing_engine/cluster_store_mapping/v1"
  }
}
hive {
  table = "cluster_store_mapping"
  database = "lake_pricing_engine"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
