dbType = "postgres"
dbName = "ops_management"
tableName = "leaves"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.ops_management.public.leaves"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "ops_management.leaves"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "ops_management/leaves/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/ops_management/leaves/v1"
  }
}
hive {
  table = "leaves"
  database = "lake_ops_management"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
