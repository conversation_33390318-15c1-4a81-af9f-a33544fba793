dbType = "postgres"
dbName = "kitchen"
tableName = "return_order"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.kitchen.public.return_order"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "kitchen.return_order"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "kitchen/return_order/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/kitchen/return_order/v1"
  }
}
hive {
  table = "return_order"
  database = "lake_kitchen"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
