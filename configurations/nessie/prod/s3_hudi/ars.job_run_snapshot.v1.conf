dbType = "mysql"
dbName = "ars"
tableName = "job_run_snapshot"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.ars.ars.job_run_snapshot"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "ars.job_run_snapshot"
    storageType = "cow"
    partitionKey = "created_at"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "ars/job_run_snapshot/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/ars/job_run_snapshot"
  }
}
  hive {
  table = "job_run_snapshot"
  database = "lake_ars"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
