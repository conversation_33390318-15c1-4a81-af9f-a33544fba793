dbType = "postgres"
dbName = "adserver"
tableName = "campaign_history"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.adserver.public.campaign_history"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_ts"
    name = "adserver.campaign_history"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "adserver/campaign_history/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/adserver/campaign_history/v1"
  }
}
hive {
  table = "campaign_history"
  database = "lake_adserver"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
