dbType = "postgres"
dbName = "seller"
tableName = "faq"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.seller.public.faq"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "seller.faq"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "seller/faq/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/seller/faq/v1"
  }
}
hive {
  table = "faq"
  database = "lake_seller"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
