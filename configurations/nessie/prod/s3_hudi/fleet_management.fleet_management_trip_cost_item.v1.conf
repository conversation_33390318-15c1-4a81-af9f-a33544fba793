dbType = "postgres"
dbName = "fleet_management"
tableName = "fleet_management_trip_cost_item"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.fleet_management.public.fleet_management_trip_cost_item"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "install_ts"
    name = "fleet_management.fleet_management_trip_cost_item"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "fleet_management/fleet_management_trip_cost_item/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/fleet_management/fleet_management_trip_cost_item/v1"
  }
}
hive {
  table = "fleet_management_trip_cost_item"
  database = "lake_fleet_management"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
