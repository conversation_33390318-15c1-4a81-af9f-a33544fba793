dbType = "postgres"
dbName = "crm"
tableName = "crm_device_block_action_history"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.crm.public.crm_device_block_action_history"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "crm.crm_device_block_action_history"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "crm/crm_device_block_action_history/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/crm/crm_device_block_action_history/v1"
  }
}
hive {
  table = "crm_device_block_action_history"
  database = "lake_crm"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
