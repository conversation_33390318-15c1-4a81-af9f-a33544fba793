dbType = "postgres"
dbName = "cms"
tableName = "gr_merchant_product_mapping"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.cms.public.gr_merchant_product_mapping"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "cms.gr_merchant_product_mapping"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "cms/gr_merchant_product_mapping/v3"
    fullPath = "s3://prod-data-lake-hudi-applications/cms/gr_merchant_product_mapping/v3"
  }
}
hive {
  table = "gr_merchant_product_mapping"
  database = "lake_cms"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
