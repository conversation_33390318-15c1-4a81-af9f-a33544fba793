dbType = "mysql"
dbName = "ars"
tableName = "ordering_override_log"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.ars.ars.ordering_override_log"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "ars.ordering_override_log"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "ars/ordering_override_log/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/ars/ordering_override_log/v1"
  }
}
hive {
  table = "ordering_override_log"
  database = "lake_ars"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
