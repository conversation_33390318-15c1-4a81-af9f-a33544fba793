dbType = "mysql"
dbName = "rpc"
tableName = "warehouse_transition"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.rpc.rpc.warehouse_transition"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "rpc.warehouse_transition"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "rpc/warehouse_transition/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/rpc/warehouse_transition/v1"
  }
}
hive {
  table = "warehouse_transition"
  database = "lake_rpc"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
