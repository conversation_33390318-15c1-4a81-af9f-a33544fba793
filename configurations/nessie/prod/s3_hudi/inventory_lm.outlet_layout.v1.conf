dbType = "postgres"
dbName = "inventory_lm"
tableName = "outlet_layout"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.inventory_lm.public.outlet_layout"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "inventory_lm.outlet_layout"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "inventory_lm/outlet_layout/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/inventory_lm/outlet_layout/v1"
  }
}
hive {
  table = "outlet_layout"
  database = "lake_inventory_lm"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
