dbType = "mysql"
dbName = "ars"
tableName = "bulk_process_ordering_override_tracker"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.ars.ars.bulk_process_ordering_override_tracker"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "ars.bulk_process_ordering_override_tracker"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "ars/bulk_process_ordering_override_tracker/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/ars/bulk_process_ordering_override_tracker/v1"
  }
}
hive {
  table = "bulk_process_ordering_override_tracker"
  database = "lake_ars"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
