dbType = "postgres"
dbName = "ops_management"
tableName = "referral_leads"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.ops_management.public.referral_leads"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "install_ts"
    piiColumns = "lead_phone_number"
    name = "ops_management.referral_leads"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "ops_management/referral_leads/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/ops_management/referral_leads/v1"
  }
}
hive {
  table = "referral_leads"
  database = "lake_ops_management"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
