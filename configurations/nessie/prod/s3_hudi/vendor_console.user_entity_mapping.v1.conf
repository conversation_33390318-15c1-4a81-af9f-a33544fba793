dbType = "postgres"
dbName = "vendor_console"
tableName = "user_entity_mapping"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.vendor_console.public.user_entity_mapping"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "vendor_console.user_entity_mapping"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "vendor_console/user_entity_mapping/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/vendor_console/user_entity_mapping/v1"
  }
}
hive {
  table = "user_entity_mapping"
  database = "lake_vendor_console"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
