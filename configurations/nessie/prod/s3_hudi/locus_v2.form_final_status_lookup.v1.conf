dbType = "postgres"
dbName = "locus_v2"
tableName = "form_final_status_lookup"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.locus_v2.public.form_final_status_lookup"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "form_id,team_id,status_id"
    partitionKey = "created_at"
    name = "locus_v2.form_final_status_lookup"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "locus_v2/form_final_status_lookup/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/locus_v2/form_final_status_lookup/v1"
  }
}
hive {
  table = "form_final_status_lookup"
  database = "lake_locus_v2"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
