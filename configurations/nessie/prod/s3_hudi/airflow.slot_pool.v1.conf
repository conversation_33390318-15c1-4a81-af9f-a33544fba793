dbType = "postgres"
dbName = "airflow"
tableName = "slot_pool"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.airflow.public.slot_pool"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "airflow.slot_pool"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "airflow/slot_pool/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/airflow/slot_pool/v1"
  }
}
hive {
  table = "slot_pool"
  database = "lake_airflow"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
