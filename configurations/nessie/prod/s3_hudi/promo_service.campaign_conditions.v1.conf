dbType = "mysql"
dbName = "promo_service"
tableName = "campaign_conditions"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.promo_service.promo_service.campaign_conditions"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "condition_id"
    name = "promo_service.campaign_conditions"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "promo_service/campaign_conditions/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/promo_service/campaign_conditions/v1"
  }
}
hive {
  table = "campaign_conditions"
  database = "lake_promo_service"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
