dbType = "postgres"
dbName = "document_digitisation"
tableName = "digitise_invoice_request"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.document_digitisation.public.digitise_invoice_request"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "document_digitisation.digitise_invoice_request"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "document_digitisation/digitise_invoice_request/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/document_digitisation/digitise_invoice_request/v1"
  }
}
hive {
  table = "digitise_invoice_request"
  database = "lake_document_digitisation"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
