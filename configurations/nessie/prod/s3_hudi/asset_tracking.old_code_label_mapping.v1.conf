dbType = "postgres"
dbName = "asset_tracking"
tableName = "old_code_label_mapping"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.asset_tracking.public.old_code_label_mapping"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "asset_tracking.old_code_label_mapping"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "asset_tracking/old_code_label_mapping/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/asset_tracking/old_code_label_mapping/v1"
  }
}
hive {
  table = "old_code_label_mapping"
  database = "lake_asset_tracking"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
