dbType = "postgres"
dbName = "ops_management"
tableName = "user_bank_details"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.ops_management.public.user_bank_details"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    piiColumns = "account_number"
    name = "ops_management.user_bank_details"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "ops_management/user_bank_details/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/ops_management/user_bank_details/v1"
  }
}
hive {
  table = "user_bank_details"
  database = "lake_ops_management"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
