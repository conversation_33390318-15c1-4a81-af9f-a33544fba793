dbType = "mysql"
dbName = "rpc"
tableName = "attribute_outlet_entity_vendor"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.rpc.rpc.attribute_outlet_entity_vendor"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "rpc.attribute_outlet_entity_vendor"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "rpc/attribute_outlet_entity_vendor/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/rpc/attribute_outlet_entity_vendor/v1"
  }
}
hive {
  table = "attribute_outlet_entity_vendor"
  database = "lake_rpc"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
