dbType = "postgres"
dbName = "cms"
tableName = "entity_requests"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.cms.public.entity_requests"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "cms.entity_requests"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "cms/entity_requests/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/cms/entity_requests/v1"
  }
}
hive {
  table = "entity_requests"
  database = "lake_cms"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
