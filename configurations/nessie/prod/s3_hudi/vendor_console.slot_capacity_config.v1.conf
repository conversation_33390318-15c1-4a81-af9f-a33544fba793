dbType = "postgres"
dbName = "vendor_console"
tableName = "slot_capacity_config"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.vendor_console.public.slot_capacity_config"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "vendor_console.slot_capacity_config"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "vendor_console/slot_capacity_config/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/vendor_console/slot_capacity_config/v1"
  }
}
hive {
  table = "slot_capacity_config"
  database = "lake_vendor_console"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
