dbType = "postgres"
dbName = "adserver_analytics"
tableName = "ba_market_basket_insights"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.adserver_analytics.public.ba_market_basket_insights"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "affinity_category_id,category_id,metric,city_name,brand_id"
    partitionKey = "start_date"
    name = "adserver_analytics.ba_market_basket_insights"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "adserver_analytics/ba_market_basket_insights/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/adserver_analytics/ba_market_basket_insights/v1"
  }
}
hive {
  table = "ba_market_basket_insights"
  database = "lake_adserver_analytics"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
