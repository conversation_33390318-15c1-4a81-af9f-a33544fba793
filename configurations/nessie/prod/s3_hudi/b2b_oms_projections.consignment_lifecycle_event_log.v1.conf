dbType = "postgres"
dbName = "b2b_oms_projections"
tableName = "consignment_lifecycle_event_log"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.b2b_oms_projections.public.consignment_lifecycle_event_log"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "event_id"
    partitionKey = "event_processing_ts"
    name = "b2b_oms_projections.consignment_lifecycle_event_log"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "b2b_oms_projections/consignment_lifecycle_event_log/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/b2b_oms_projections/consignment_lifecycle_event_log/v1"
  }
}
hive {
  table = "consignment_lifecycle_event_log"
  database = "lake_b2b_oms_projections"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
