dbType = "postgres"
dbName = "search_management"
tableName = "suggestion_value"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.search_management.public.suggestion_value"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "search_management.suggestion_value"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "search_management/suggestion_value/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/search_management/suggestion_value/v1"
  }
}
hive {
  table = "suggestion_value"
  database = "lake_search_management"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
