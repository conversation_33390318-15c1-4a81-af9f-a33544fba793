dbType = "postgres"
dbName = "inventory_lm"
tableName = "outlet_floor_shortest_path"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.inventory_lm.public.outlet_floor_shortest_path"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "version_id"
    name = "inventory_lm.outlet_floor_shortest_path"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "inventory_lm/outlet_floor_shortest_path/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/inventory_lm/outlet_floor_shortest_path/v1"
  }
}
hive {
  table = "outlet_floor_shortest_path"
  database = "lake_inventory_lm"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
