dbType = "postgres"
dbName = "logistics_insights"
tableName = "carriers"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.logistics_insights.public.carriers"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "logistics_insights.carriers"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "logistics_insights/carriers/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/logistics_insights/carriers/v1"
  }
}
hive {
  table = "carriers"
  database = "lake_logistics_insights"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
