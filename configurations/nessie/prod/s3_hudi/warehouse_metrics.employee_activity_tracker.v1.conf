dbType = "postgres"
dbName = "warehouse_metrics"
tableName = "employee_activity_tracker"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.warehouse_metrics.public.employee_activity_tracker"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "warehouse_metrics.employee_activity_tracker"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "warehouse_metrics/employee_activity_tracker/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/warehouse_metrics/employee_activity_tracker/v1"
  }
}
hive {
  table = "employee_activity_tracker"
  database = "lake_warehouse_metrics"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
