dbType = "mysql"
dbName = "wms"
tableName = "vehicle_registry"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.wms.wms.vehicle_registry"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "wms.vehicle_registry"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "wms/vehicle_registry/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/wms/vehicle_registry/v1"
  }
}
hive {
  table = "vehicle_registry"
  database = "lake_wms"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
