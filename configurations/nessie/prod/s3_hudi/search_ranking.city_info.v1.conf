dbType = "postgres"
dbName = "search_ranking"
tableName = "city_info"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.search_ranking.public.city_info"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "city_id"
    name = "search_ranking.city_info"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "search_ranking/city_info/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/search_ranking/city_info/v1"
  }
}
hive {
  table = "city_info"
  database = "lake_search_ranking"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
