dbType = "postgres"
dbName = "fleet_management"
tableName = "fleet_management_cost_item_request_mapping"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.fleet_management.public.fleet_management_cost_item_request_mapping"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "fleet_management.fleet_management_cost_item_request_mapping"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "fleet_management/fleet_management_cost_item_request_mapping/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/fleet_management/fleet_management_cost_item_request_mapping/v1"
  }
}
hive {
  table = "fleet_management_cost_item_request_mapping"
  database = "lake_fleet_management"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
