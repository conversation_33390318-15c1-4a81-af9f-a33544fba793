dbType = "postgres"
dbName = "adserver"
tableName = "slot_booking"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.adserver.public.slot_booking"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_ts"
    name = "adserver.slot_booking"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "adserver/slot_booking/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/adserver/slot_booking/v1"
  }
}
hive {
  table = "slot_booking"
  database = "lake_adserver"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
