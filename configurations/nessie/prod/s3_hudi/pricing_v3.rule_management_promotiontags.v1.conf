dbType = "postgres"
dbName = "pricing_v3"
tableName = "rule_management_promotiontags"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.pricing_v3.public.rule_management_promotiontags"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "pricing_v3.rule_management_promotiontags"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "pricing_v3/rule_management_promotiontags/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/pricing_v3/rule_management_promotiontags/v1"
  }
}
hive {
  table = "rule_management_promotiontags"
  database = "lake_pricing_v3"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
