dbType = "postgres"
dbName = "adserver"
tableName = "ba_membership"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.adserver.public.ba_membership"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "adserver.ba_membership"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "adserver/ba_membership/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/adserver/ba_membership/v1"
  }
}
hive {
  table = "ba_membership"
  database = "lake_adserver"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
