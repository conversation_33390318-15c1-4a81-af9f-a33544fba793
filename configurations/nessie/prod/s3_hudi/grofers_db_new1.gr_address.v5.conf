dbType = "postgres"
dbName = "grofers_db_new1"
tableName = "gr_address"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.grofers_db_new1.public.gr_address"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "grofers_db_new1.gr_address"
    storageType = "mor"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "grofers_db_new1/gr_address/v3"
    fullPath = "s3://prod-data-lake-hudi-applications/grofers_db_new1/gr_address/v3"
  }
}
hive {
  table = "gr_address_mor_test_v2"
  database = "lake_grofers_db"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
