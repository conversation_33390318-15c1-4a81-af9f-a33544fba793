dbType = "postgres"
dbName = "seller"
tableName = "item_outlet_inventory"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.seller.public.item_outlet_inventory"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "seller.item_outlet_inventory"
    storageType = "cow"
    partitionKey = "outlet_id"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "seller/item_outlet_inventory/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/seller/item_outlet_inventory/v1"
  }
}
hive {
  table = "item_outlet_inventory"
  database = "lake_seller"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
