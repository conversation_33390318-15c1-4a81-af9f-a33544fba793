dbType = "postgres"
dbName = "ops_management"
tableName = "employee_tenant_map"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.ops_management.public.employee_tenant_map"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "install_ts"
    name = "ops_management.employee_tenant_map"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "ops_management/employee_tenant_map/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/ops_management/employee_tenant_map/v1"
  }
}
hive {
  table = "employee_tenant_map"
  database = "lake_ops_management"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
