dbType = "postgres"
dbName = "airflow"
tableName = "task_instance"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.airflow.public.task_instance"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "task_id,dag_id,run_id,map_index"
    partitionKey = "start_date"
    name = "airflow.task_instance"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "airflow/task_instance/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/airflow/task_instance/v1"
  }
}
hive {
  table = "task_instance"
  database = "lake_airflow"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
