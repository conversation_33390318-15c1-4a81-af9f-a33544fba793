dbType = "postgres"
dbName = "iot"
tableName = "notification_condition"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.iot.public.notification_condition"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "iot.notification_condition"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "iot/notification_condition/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/iot/notification_condition/v1"
  }
}
hive {
  table = "notification_condition"
  database = "lake_iot"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
