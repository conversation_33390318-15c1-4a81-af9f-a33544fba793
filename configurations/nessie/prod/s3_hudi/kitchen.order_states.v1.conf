dbType = "postgres"
dbName = "kitchen"
tableName = "order_states"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.kitchen.public.order_states"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "kitchen.order_states"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "kitchen/order_states/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/kitchen/order_states/v1"
  }
}
hive {
  table = "order_states"
  database = "lake_kitchen"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
