dbType = "postgres"
dbName = "b2b_oms"
tableName = "consignment_transaction_entity"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.b2b_oms.public.consignment_transaction_entity"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "b2b_oms.consignment_transaction_entity"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "b2b_oms/consignment_transaction_entity/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/b2b_oms/consignment_transaction_entity/v1"
  }
}
hive {
  table = "consignment_transaction_entity"
  database = "lake_b2b_oms"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
