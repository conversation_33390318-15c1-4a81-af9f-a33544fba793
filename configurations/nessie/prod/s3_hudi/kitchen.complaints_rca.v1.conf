dbType = "postgres"
dbName = "kitchen"
tableName = "complaints_rca"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.kitchen.public.complaints_rca"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "kitchen.complaints_rca"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "kitchen/complaints_rca/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/kitchen/complaints_rca/v1"
  }
}
hive {
  table = "complaints_rca"
  database = "lake_kitchen"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
