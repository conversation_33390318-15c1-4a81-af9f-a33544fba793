dbType = "postgres"
dbName = "layout_service_preprod"
tableName = "widgets"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.layout_service_preprod.public.widgets"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "layout_service_preprod.widgets"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "layout_service_preprod/widgets/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/layout_service_preprod/widgets/v1"
  }
}
hive {
  table = "widgets"
  database = "lake_layout_service_preprod"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
