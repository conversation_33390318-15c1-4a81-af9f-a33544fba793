dbType = "mysql"
dbName = "wms"
tableName = "bad_stock_container"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.wms.wms.bad_stock_container"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "wms.bad_stock_container"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "wms/bad_stock_container/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/wms/bad_stock_container/v1"
  }
}
hive {
  table = "bad_stock_container"
  database = "lake_wms"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
