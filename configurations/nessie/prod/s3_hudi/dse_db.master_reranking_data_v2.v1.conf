dbType = "postgres"
dbName = "dse_db"
tableName = "master_reranking_data_v2"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.dse_db.public.master_reranking_data_v2"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "dse_db.master_reranking_data_v2"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "dse_db/master_reranking_data_v2/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/dse_db/master_reranking_data_v2/v1"
  }
}
hive {
  table = "master_reranking_data_v2"
  database = "lake_dse_db"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
