dbType = "postgres"
dbName = "iot"
tableName = "notification_log"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.iot.public.notification_log"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "iot.notification_log"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "iot/notification_log/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/iot/notification_log/v1"
  }
}
hive {
  table = "notification_log"
  database = "lake_iot"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
