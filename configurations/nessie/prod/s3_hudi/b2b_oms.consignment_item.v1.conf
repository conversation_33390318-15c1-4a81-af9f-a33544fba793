dbType = "postgres"
dbName = "b2b_oms"
tableName = "consignment_item"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.b2b_oms.public.consignment_item"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "b2b_oms.consignment_item"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "b2b_oms/consignment_item/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/b2b_oms/consignment_item/v1"
  }
}
hive {
  table = "consignment_item"
  database = "lake_b2b_oms"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
