dbType = "postgres"
dbName = "vendor_console"
tableName = "vendor_facility_auto_release"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.vendor_console.public.vendor_facility_auto_release"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "vendor_console.vendor_facility_auto_release"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "vendor_console/vendor_facility_auto_release/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/vendor_console/vendor_facility_auto_release/v1"
  }
}
hive {
  table = "vendor_facility_auto_release"
  database = "lake_vendor_console"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
