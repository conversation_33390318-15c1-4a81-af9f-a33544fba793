dbType = "mysql"
dbName = "po"
tableName = "asn_item_level_validations"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.po.po.asn_item_level_validations"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "po.asn_item_level_validations"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "po/asn_item_level_validations/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/po/asn_item_level_validations/v1"
  }
}
hive {
  table = "asn_item_level_validations"
  database = "lake_po"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
