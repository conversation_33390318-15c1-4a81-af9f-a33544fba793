dbType = "postgres"
dbName = "user_management"
tableName = "enrollments"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.user_management.public.enrollments"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "user_management.enrollments"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "user_management/enrollments/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/user_management/enrollments/v1"
  }
}
hive {
  table = "enrollments"
  database = "lake_user_management"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
