dbType = "postgres"
dbName = "seller"
tableName = "cart_transaction_log"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.seller.public.cart_transaction_log"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "seller.cart_transaction_log"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "seller/cart_transaction_log/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/seller/cart_transaction_log/v1"
  }
}
hive {
  table = "cart_transaction_log"
  database = "lake_seller"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
