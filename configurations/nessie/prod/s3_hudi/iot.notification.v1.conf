dbType = "postgres"
dbName = "iot"
tableName = "notification"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.iot.public.notification"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "iot.notification"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "iot/notification/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/iot/notification/v1"
  }
}
hive {
  table = "notification"
  database = "lake_iot"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
