dbType = "postgres"
dbName = "irs"
tableName = "vendor_functionality_whitelisting"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.irs.public.vendor_functionality_whitelisting"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "irs.vendor_functionality_whitelisting"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "irs/vendor_functionality_whitelisting/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/irs/vendor_functionality_whitelisting/v1"
  }
}
hive {
  table = "vendor_functionality_whitelisting"
  database = "lake_irs"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
