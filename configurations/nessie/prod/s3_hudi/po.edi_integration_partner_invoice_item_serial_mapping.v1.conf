dbType = "mysql"
dbName = "po"
tableName = "edi_integration_partner_invoice_item_serial_mapping"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.po.po.edi_integration_partner_invoice_item_serial_mapping"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "po.edi_integration_partner_invoice_item_serial_mapping"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "po/edi_integration_partner_invoice_item_serial_mapping/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/po/edi_integration_partner_invoice_item_serial_mapping/v1"
  }
}
hive {
  table = "edi_integration_partner_invoice_item_serial_mapping"
  database = "lake_po"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
