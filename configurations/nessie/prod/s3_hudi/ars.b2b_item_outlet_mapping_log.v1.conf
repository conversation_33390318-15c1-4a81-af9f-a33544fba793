dbType = "mysql"
dbName = "ars"
tableName = "b2b_item_outlet_mapping_log"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.ars.ars.b2b_item_outlet_mapping_log"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "ars.b2b_item_outlet_mapping_log"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "ars/b2b_item_outlet_mapping_log/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/ars/b2b_item_outlet_mapping_log/v1"
  }
}
hive {
  table = "b2b_item_outlet_mapping_log"
  database = "lake_ars"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
