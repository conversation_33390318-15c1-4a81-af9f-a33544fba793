dbType = "postgres"
dbName = "cms"
tableName = "entity_rules"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.cms.public.entity_rules"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "cms.entity_rules"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "cms/entity_rules/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/cms/entity_rules/v1"
  }
}
hive {
  table = "entity_rules"
  database = "lake_cms"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
