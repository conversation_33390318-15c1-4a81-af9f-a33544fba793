dbType = "mysql"
dbName = "ars"
tableName = "transfer_case_size_log"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.ars.ars.transfer_case_size_log"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "ars.transfer_case_size_log"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "ars/transfer_case_size_log/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/ars/transfer_case_size_log/v1"
  }
}
hive {
  table = "transfer_case_size_log"
  database = "lake_ars"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
