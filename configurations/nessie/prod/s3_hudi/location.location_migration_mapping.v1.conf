dbType = "postgres"
dbName = "location"
tableName = "location_migration_mapping"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.location.public.location_migration_mapping"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "outlet_id"
    name = "location.location_migration_mapping"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "location/location_migration_mapping/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/location/location_migration_mapping/v1"
  }
}
hive {
  table = "location_migration_mapping"
  database = "lake_location"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
