dbType = "postgres"
dbName = "irs"
tableName = "legal_identifier"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.irs.public.legal_identifier"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "irs.legal_identifier"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "irs/legal_identifier/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/irs/legal_identifier/v1"
  }
}
hive {
  table = "legal_identifier"
  database = "lake_irs"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
