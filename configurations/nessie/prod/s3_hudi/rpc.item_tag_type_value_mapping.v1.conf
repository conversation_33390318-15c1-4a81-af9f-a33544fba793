dbType = "mysql"
dbName = "rpc"
tableName = "item_tag_type_value_mapping"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.rpc.rpc.item_tag_type_value_mapping"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "rpc.item_tag_type_value_mapping"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "rpc/item_tag_type_value_mapping/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/rpc/item_tag_type_value_mapping/v1"
  }
}
hive {
  table = "item_tag_type_value_mapping"
  database = "lake_rpc"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
