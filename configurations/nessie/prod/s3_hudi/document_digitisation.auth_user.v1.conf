dbType = "postgres"
dbName = "document_digitisation"
tableName = "auth_user"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.document_digitisation.public.auth_user"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "document_digitisation.auth_user"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "document_digitisation/auth_user/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/document_digitisation/auth_user/v1"
  }
}
hive {
  table = "auth_user"
  database = "lake_document_digitisation"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
