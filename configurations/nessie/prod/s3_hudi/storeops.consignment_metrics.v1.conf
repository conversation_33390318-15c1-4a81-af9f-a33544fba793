dbType = "postgres"
dbName = "storeops"
tableName = "consignment_metrics"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.storeops.public.consignment_metrics"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_date"
    name = "storeops.consignment_metrics"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "storeops/consignment_metrics/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/storeops/consignment_metrics/v1"
  }
}
hive {
  table = "consignment_metrics"
  database = "lake_storeops"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
