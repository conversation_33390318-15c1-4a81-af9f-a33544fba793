dbType = "mysql"
dbName = "rpc"
tableName = "product_tax"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.rpc.rpc.product_tax"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "rpc.product_tax"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "rpc/product_tax/v3"
    fullPath = "s3://prod-data-lake-hudi-applications/rpc/product_tax/v3"
  }
}
hive {
  table = "product_tax"
  database = "lake_rpc"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
