dbType = "postgres"
dbName = "kitchen"
tableName = "entity_schedule"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.kitchen.public.entity_schedule"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "kitchen.entity_schedule"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "kitchen/entity_schedule/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/kitchen/entity_schedule/v1"
  }
}
hive {
  table = "entity_schedule"
  database = "lake_kitchen"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
