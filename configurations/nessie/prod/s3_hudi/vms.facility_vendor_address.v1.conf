dbType = "mysql"
dbName = "vms"
tableName = "facility_vendor_address"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.vms.vms.facility_vendor_address"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "vms.facility_vendor_address"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "vms/facility_vendor_address/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/vms/facility_vendor_address/v1"
  }
}
hive {
  table = "facility_vendor_address"
  database = "lake_vms"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
