dbType = "mysql"
dbName = "promo_service"
tableName = "user"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.promo_service.promo_service.user"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "user_id"
    name = "promo_service.user"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "promo_service/user/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/promo_service/user/v1"
  }
}
hive {
  table = "user"
  database = "lake_promo_service"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
