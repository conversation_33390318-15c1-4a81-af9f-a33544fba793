dbType = "postgres"
dbName = "seller"
tableName = "recall_request"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.seller.public.recall_request"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "seller.recall_request"
    storageType = "cow"
    partitionKey = "created_at"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "seller/recall_request/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/seller/recall_request/v1"
  }
}
hive {
  table = "recall_request"
  database = "lake_seller"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
