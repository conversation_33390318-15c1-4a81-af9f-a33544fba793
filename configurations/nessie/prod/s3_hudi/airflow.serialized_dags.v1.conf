dbType = "postgres"
dbName = "airflow"
tableName = "serialized_dags"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.airflow.public.serialized_dags"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "dag_id"
    partitionKey = "last_updated"
    name = "airflow.serialized_dags"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "airflow/serialized_dags/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/airflow/serialized_dags/v1"
  }
}
hive {
  table = "serialized_dags"
  database = "lake_airflow"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
