dbType = "postgres"
dbName = "irs"
tableName = "legal_entity"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.irs.public.legal_entity"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    partitionKey = "created_at"
    name = "irs.legal_entity"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "irs/legal_entity/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/irs/legal_entity/v1"
  }
}
hive {
  table = "legal_entity"
  database = "lake_irs"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
