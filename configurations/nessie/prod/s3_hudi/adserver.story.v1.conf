dbType = "postgres"
dbName = "adserver"
tableName = "story"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "postgres.adserver.public.story"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "adserver.story"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "adserver/story/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/adserver/story/v1"
  }
}
hive {
  table = "story"
  database = "lake_adserver"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
