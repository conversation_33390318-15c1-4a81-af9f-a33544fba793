dbType = "mysql"
dbName = "rpc"
tableName = "transfer_case_size"
schemaRegistryUrl = "http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081"
s3 {
  topicName = "mysql.rpc.rpc.transfer_case_size"
  maxBatchRead = 400
  bucket {
    uri = "s3://prod-data-debezium-raw"
  }
}
hudi {
  table {
    primaryKey = "id"
    name = "rpc.transfer_case_size"
    storageType = "cow"
  }
  bucket {
    uri = "s3://prod-data-lake-hudi-applications"
    prefix = "rpc/transfer_case_size/v1"
    fullPath = "s3://prod-data-lake-hudi-applications/rpc/transfer_case_size/v1"
  }
}
hive {
  table = "transfer_case_size"
  database = "lake_rpc"
}
lakeCredentialsPath = "data/services/source-replication/nessie/iam-roles/prod-dse-datalake"
