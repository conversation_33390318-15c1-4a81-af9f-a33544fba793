{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.base_model_csvs,public.base_sla_metrics,public.base_sla_model_runs,public.carriers,public.cities,public.cron_executions,public.files,public.input_filters,public.input_groups,public.order_projections,public.roles,public.slots,public.snapshot_inputs,public.snapshots,public.stores,public.tags,public.user_runs,public.users", "message.key.columns": "public.base_model_csvs:id;public.base_sla_metrics:id;public.base_sla_model_runs:id;public.carriers:id;public.cities:id;public.cron_executions:id;public.files:id;public.input_filters:id;public.input_groups:id;public.order_projections:id;public.roles:id;public.slots:id;public.snapshot_inputs:id;public.snapshots:id;public.stores:id;public.tags:id;public.user_runs:id;public.users:id", "name": "postgres.logistics_insights.public.table_group_1.v2", "snapshot.mode": "exported", "database.server.name": "postgres.logistics_insights", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "logistics_insights", "slot.drop.on.stop": "false", "slot.name": "postgres_logistics_insights_public_table_group_1_v2", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.logistics_insights.public.table_group_1.v2"}