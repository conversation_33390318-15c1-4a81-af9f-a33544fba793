{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.widgets", "message.key.columns": "public.widgets:id", "name": "postgres.layout_service_preprod.public.table_group_1.v1", "snapshot.mode": "exported", "database.server.name": "postgres.layout_service_preprod", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "layout_service_preprod", "slot.drop.on.stop": "false", "slot.name": "postgres_layout_service_preprod_public_table_group_1_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.layout_service_preprod.public.table_group_1.v1"}