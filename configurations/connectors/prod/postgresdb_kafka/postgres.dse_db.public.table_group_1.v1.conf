{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.because_you_bought_usecase_v2,public.complementary_products,public.frequently_bought_groups_meta,public.personalisation_story_metadata,public.story_information,public.trending_entities,public.master_reranking_data_v2", "message.key.columns": "public.because_you_bought_usecase_v2:collection_uuid;public.complementary_products:antecedent_pid,consequent_ptype;public.frequently_bought_groups_meta:group_id,is_enabled;public.personalisation_story_metadata:id;public.story_information:id;public.trending_entities:id;public.master_reranking_data_v2:id", "name": "postgres.dse_db.public.table_group_1.v1", "snapshot.mode": "exported", "database.server.name": "postgres.dse_db", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "dse_db", "slot.drop.on.stop": "false", "slot.name": "postgres_dse_db_public_table_group_1_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.dse_db.public.table_group_1.v1"}