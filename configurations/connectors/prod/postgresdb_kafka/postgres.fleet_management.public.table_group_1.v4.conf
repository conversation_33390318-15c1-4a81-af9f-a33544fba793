{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.fleet_management_approval_request,public.fleet_management_device,public.fleet_management_distance_mapping,public.fleet_management_node_billable_city_mapping,public.fleet_management_predicate,public.fleet_management_rule,public.fleet_management_tolls_cost,public.fleet_management_transaction_logs,public.fleet_management_trip_adhoc_costs_attributes,public.fleet_management_trips_cost,public.fleet_management_truck_management,public.fleet_management_truck_vendor_mapping,public.fleet_management_user,public.fleet_management_user_address,public.fleet_management_user_detail,public.fleet_management_user_detail_store_mapping,public.fleet_management_user_document,public.fleet_management_vehicle,public.fleet_management_vehicle_detail,public.fleet_management_vendor,public.fleet_management_vendor_rate_card_config,public.fleet_management_vendor_rate_card_config_rule_mapping", "message.key.columns": "public.fleet_management_approval_request:id;public.fleet_management_device:id;public.fleet_management_distance_mapping:id;public.fleet_management_node_billable_city_mapping:id;public.fleet_management_predicate:id;public.fleet_management_rule:id;public.fleet_management_tolls_cost:id;public.fleet_management_transaction_logs:id;public.fleet_management_trip_adhoc_costs_attributes:id;public.fleet_management_trips_cost:id;public.fleet_management_truck_management:id;public.fleet_management_truck_vendor_mapping:id;public.fleet_management_user:id;public.fleet_management_user_address:id;public.fleet_management_user_detail:id;public.fleet_management_user_detail_store_mapping:id;public.fleet_management_user_document:id;public.fleet_management_vehicle:id;public.fleet_management_vehicle_detail:id;public.fleet_management_vendor:id;public.fleet_management_vendor_rate_card_config:id;public.fleet_management_vendor_rate_card_config_rule_mapping:id", "name": "postgres.fleet_management.public.table_group_1.v4", "snapshot.mode": "never", "database.server.name": "postgres.fleet_management", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "fleet_management", "slot.drop.on.stop": "false", "slot.name": "postgres_fleet_management_public_table_group_1_v4", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20", "tombstones.on.delete": "false"}, "name": "postgres.fleet_management.public.table_group_1.v4"}