{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.activity_logs,public.attendance_summary,public.complaints_rca,public.dish,public.entity,public.entity_schedule,public.foodware_mapping,public.ingredient,public.item_mapping,public.order_states,public.orders,public.packaging_bags,public.preparation_task_log,public.product,public.product_mapping,public.product_transformation,public.return_order,public.roster_planner,public.shift_record,public.station,public.station_product_mapping,public.store_product_mapping,public.store_station,public.sub_order,public.sub_order_item,public.subdish,public.user_entity_mapping", "message.key.columns": "public.activity_logs:id;public.attendance_summary:id;public.complaints_rca:id;public.dish:id;public.entity:id;public.entity_schedule:id;public.foodware_mapping:id;public.ingredient:id;public.item_mapping:id;public.order_states:id;public.orders:id;public.packaging_bags:id;public.preparation_task_log:id;public.product:id;public.product_mapping:id;public.product_transformation:id;public.return_order:id;public.roster_planner:id;public.shift_record:id;public.station:id;public.station_product_mapping:id;public.store_product_mapping:id;public.store_station:id;public.sub_order:id;public.sub_order_item:id;public.subdish:id;public.user_entity_mapping:id", "name": "postgres.kitchen.public.table_group_1.v2", "snapshot.mode": "exported", "database.server.name": "postgres.kitchen", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "kitchen", "slot.drop.on.stop": "false", "slot.name": "postgres_kitchen_public_table_group_1_v2", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.kitchen.public.table_group_1.v2"}