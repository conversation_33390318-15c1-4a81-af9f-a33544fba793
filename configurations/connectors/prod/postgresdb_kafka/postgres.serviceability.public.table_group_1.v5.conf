{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.ser_disruption_request,public.ser_disruption_schedule,public.ser_distance_based_surge_logs,public.ser_entity_metrics([_](default|p\\d{4}w(0[1-9]|[1-4][0-9]|5[0-3]))),public.ser_node,public.ser_polygon_refresh_logs,public.ser_polygons,public.ser_rain_events,public.ser_store_config,public.ser_store_config_change_logs,public.ser_store_polygon_logs,public.ser_store_polygon_schedule,public.ser_store_polygons,public.surge_di_based_with_mov", "message.key.columns": "public.ser_disruption_request:id;public.ser_disruption_schedule:id;public.ser_distance_based_surge_logs:id;public.ser_entity_metrics:id;public.ser_node:id;public.ser_polygon_refresh_logs:id;public.ser_polygons:id;public.ser_rain_events:id;public.ser_store_config:id;public.ser_store_config_change_logs:id;public.ser_store_polygon_logs:id;public.ser_store_polygon_schedule:id;public.ser_store_polygons:id;public.surge_di_based_with_mov:id", "name": "postgres.serviceability.public.table_group_1.v5", "snapshot.mode": "never", "database.server.name": "postgres.serviceability", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "serviceability", "slot.drop.on.stop": "false", "slot.name": "postgres_serviceability_public_table_group_1_v5", "transforms": "Reroute_ser_entity_metrics", "transforms.Reroute_ser_entity_metrics.type": "io.debezium.transforms.ByLogicalTableRouter", "transforms.Reroute_ser_entity_metrics.topic.regex": "(.*)ser_entity_metrics([_](default|p\\d{4}w(0[1-9]|[1-4][0-9]|5[0-3])))$", "transforms.Reroute_ser_entity_metrics.topic.replacement": "$1ser_entity_metrics", "transforms.Reroute_ser_entity_metrics.key.enforce.uniqueness": "false", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.serviceability.public.table_group_1.v5"}