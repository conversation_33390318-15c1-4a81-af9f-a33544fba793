{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.employee_activity_tracker,public.outbound_demand,public.report_registry", "message.key.columns": "public.employee_activity_tracker:id;public.outbound_demand:demand_id;public.report_registry:id", "name": "postgres.warehouse_metrics.public.table_group_1.v1", "snapshot.mode": "exported", "database.server.name": "postgres.warehouse_metrics", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "warehouse_metrics", "slot.drop.on.stop": "false", "slot.name": "postgres_warehouse_metrics_public_table_group_1_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.warehouse_metrics.public.table_group_1.v1"}