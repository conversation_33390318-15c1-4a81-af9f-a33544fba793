{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.queries", "message.key.columns": "public.queries:id", "name": "postgres.redash_reports.public.table_group_2.v4", "snapshot.mode": "never", "database.server.name": "postgres.redash_reports", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "redash_reports", "slot.drop.on.stop": "false", "slot.name": "postgres_redash_reports_public_table_group_2_v4", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20", "column.exclude.list": "public.queries.query"}, "name": "postgres.redash_reports.public.table_group_2.v4"}