{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.queries", "message.key.columns": "public.queries:id", "name": "postgres.redash_queries.public.table_group_2.v7", "snapshot.mode": "exported", "database.server.name": "postgres.redash_queries", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "redash_queries", "slot.drop.on.stop": "false", "slot.name": "postgres_redash_queries_public_table_group_2_v7", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20", "producer.override.max.request.size": "3145728", "column.exclude.list": "public.queries.query"}, "name": "postgres.redash_queries.public.table_group_2.v7"}