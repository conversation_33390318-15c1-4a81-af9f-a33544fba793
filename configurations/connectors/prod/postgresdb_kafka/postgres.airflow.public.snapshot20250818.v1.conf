{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.serialized_dags", "message.key.columns": "public.serialized_dags:dag_id", "name": "postgres.airflow.public.snapshot20250818.v1", "snapshot.mode": "exported", "database.server.name": "postgres.airflow", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "airflow", "slot.drop.on.stop": "false", "slot.name": "postgres_airflow_public_snapshot20250818_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.airflow.public.snapshot20250818.v1"}