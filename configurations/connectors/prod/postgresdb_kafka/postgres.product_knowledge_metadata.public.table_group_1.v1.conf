{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.external_entity_meta_mappings,public.generated_tag,public.generated_tag_product_mapping,public.keyword,public.keyword_composition,public.mapped_entities,public.metadata,public.metadata_attribute_mapping,public.metadata_category_mapping,public.metadata_collection_tags,public.metadata_images,public.metadata_relationship,public.metadata_similarity,public.metadata_tags,public.metadata_usecase,public.product_metadata_relationship,public.user_query", "message.key.columns": "public.external_entity_meta_mappings:id;public.generated_tag:id;public.generated_tag_product_mapping:id;public.keyword:id;public.keyword_composition:id;public.mapped_entities:id;public.metadata:id;public.metadata_attribute_mapping:id;public.metadata_category_mapping:id;public.metadata_collection_tags:id;public.metadata_images:id;public.metadata_relationship:id;public.metadata_similarity:id;public.metadata_tags:id;public.metadata_usecase:id;public.product_metadata_relationship:id;public.user_query:id", "name": "postgres.product_knowledge_metadata.public.table_group_1.v1", "snapshot.mode": "exported", "database.server.name": "postgres.product_knowledge_metadata", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "product_knowledge_metadata", "slot.drop.on.stop": "false", "slot.name": "postgres_product_knowledge_metadata_public_table_group_1_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.product_knowledge_metadata.public.table_group_1.v1"}