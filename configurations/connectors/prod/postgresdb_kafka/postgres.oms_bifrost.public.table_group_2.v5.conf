{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.oms_cart,public.oms_cart_address,public.oms_internal_order_mapping,public.oms_order_event,public.oms_order_item_cancellation,public.oms_procurement_update,public.oms_reason,public.oms_suborder,public.oms_suborder_event", "message.key.columns": "public.oms_cart:id;public.oms_cart_address:id;public.oms_internal_order_mapping:id;public.oms_order_event:id;public.oms_order_item_cancellation:id;public.oms_procurement_update:id;public.oms_reason:id;public.oms_suborder:id;public.oms_suborder_event:id", "name": "postgres.oms_bifrost.public.table_group_2.v5", "snapshot.mode": "never", "database.server.name": "postgres.oms_bifrost", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "oms_bifrost", "slot.drop.on.stop": "false", "slot.name": "postgres_oms_bifrost_public_table_group_2_v5", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "2097152", "producer.override.max.request.size": "20971520", "slot.max.retries": "20", "tombstones.on.delete": "false", "column.exclude.list": "public.oms_order_delayed_reschedule.id_int,public.oms_order_delayed_reschedule.order_int,public.oms_suborder.backend_merchant_id_new,public.oms_suborder.backend_merchant_id_old"}, "name": "postgres.oms_bifrost.public.table_group_2.v5"}