{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.layout_component,public.legend_mapping_detail,public.location,public.location_component_location_type_mapping,public.location_components_config,public.location_migration_mapping,public.location_nomenclature,public.location_type,public.location_zone_mapping,public.location_zone_rule,public.outlet_layout,public.outlet_location,public.outlet_zone,public.zone,public.zone_entity_rule", "message.key.columns": "public.layout_component:id;public.legend_mapping_detail:id;public.location:location_id,outlet_id,tenant_id;public.location_component_location_type_mapping:id;public.location_components_config:id;public.location_migration_mapping:id;public.location_nomenclature:id;public.location_type:name,tenant_id;public.location_zone_mapping:tenant_id,outlet_id,location_id,zone_id;public.location_zone_rule:id;public.outlet_layout:id;public.outlet_location:id;public.outlet_zone:outlet_id,zone_id,tenant_id;public.zone:zone_id,tenant_id;public.zone_entity_rule:id", "name": "postgres.location.public.table_group_1.v1", "snapshot.mode": "exported", "database.server.name": "postgres.location", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "location", "slot.drop.on.stop": "false", "slot.name": "postgres_location_public_table_group_1_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.location.public.table_group_1.v1"}