{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.gr_cart,public.gr_crm_order_event_message_mapping,public.gr_gstin,public.gr_order_cancellation_reason_mapping,public.gr_temp_user,public.gr_user_gstin_mapping,public.gr_wallet_history,public.push_notifications_apnsdevice", "message.key.columns": "public.gr_cart:id;public.gr_crm_order_event_message_mapping:id;public.gr_gstin:gstin;public.gr_order_cancellation_reason_mapping:id;public.gr_temp_user:id;public.gr_user_gstin_mapping:id;public.gr_wallet_history:id;public.push_notifications_apnsdevice:id", "name": "postgres.grofers_db_new1.public.table_group_1.v5", "snapshot.mode": "never", "database.server.name": "postgres.grofers_db_new1", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "grofers_db_new1", "slot.drop.on.stop": "false", "slot.name": "postgres_grofers_db_new1_public_table_group_1_v5", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20", "tombstones.on.delete": "false"}, "name": "postgres.grofers_db_new1.public.table_group_1.v5"}