{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.shipments_shipmentallocationstatelog,public.shipments_shipmentstatelog,public.task_management_taskstatelog,public.transit_address,public.transit_allocation_batch,public.transit_allocation_request,public.transit_allocation_vehicle,public.transit_allocation_vehicle_label,public.transit_consignment,public.transit_consignment_container,public.transit_consignment_document,public.transit_consignment_item,public.transit_consignment_state_logs,public.transit_discrepancy,public.transit_discrepancy_rca,public.transit_discrepancy_reason_type,public.transit_discrepancy_resolution_action_type,public.transit_express_allocation_field_executive_queue,public.transit_file_storage,public.transit_fleet_plan,public.transit_fleet_plan_dispatch_slot,public.transit_fleet_plan_slot_assigned_vehicle,public.transit_location,public.transit_location_rule_trigger,public.transit_location_rule_trigger_log,public.transit_node,public.transit_node_address,public.transit_projection_consignment,public.transit_projection_trip,public.transit_projection_trip_event_timeline,public.transit_shipment,public.transit_shipment_allocation,public.transit_shipment_label,public.transit_task,public.transit_trip_error_log,public.transit_trip_metadata,public.transit_user,public.transit_user_profile,public.transit_workflow,public.transit_workflow_step,public.transit_workflow_type", "message.key.columns": "public.shipments_shipmentallocationstatelog:id;public.shipments_shipmentstatelog:id;public.task_management_taskstatelog:id;public.transit_address:id;public.transit_allocation_batch:id;public.transit_allocation_request:id;public.transit_allocation_vehicle:id;public.transit_allocation_vehicle_label:id;public.transit_consignment:id;public.transit_consignment_container:id;public.transit_consignment_document:id;public.transit_consignment_item:id;public.transit_consignment_state_logs:id;public.transit_discrepancy:id;public.transit_discrepancy_rca:id;public.transit_discrepancy_reason_type:id;public.transit_discrepancy_resolution_action_type:id;public.transit_express_allocation_field_executive_queue:id;public.transit_file_storage:id;public.transit_fleet_plan:id;public.transit_fleet_plan_dispatch_slot:id;public.transit_fleet_plan_slot_assigned_vehicle:id;public.transit_location:id;public.transit_location_rule_trigger:id;public.transit_location_rule_trigger_log:id;public.transit_node:id;public.transit_node_address:id;public.transit_projection_consignment:consignment_id;public.transit_projection_trip:trip_id;public.transit_projection_trip_event_timeline:id;public.transit_shipment:id;public.transit_shipment_allocation:id;public.transit_shipment_label:id;public.transit_task:id;public.transit_trip_error_log:id;public.transit_trip_metadata:id;public.transit_user:id;public.transit_user_profile:id;public.transit_workflow:id;public.transit_workflow_step:id;public.transit_workflow_type:id", "name": "postgres.transit_server.public.table_group_1.v1", "snapshot.mode": "exported", "database.server.name": "postgres.transit_server", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "transit_server", "slot.drop.on.stop": "false", "slot.name": "postgres_transit_server_public_table_group_1_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "producer.override.max.request.size": "3145728", "slot.max.retries": "20"}, "name": "postgres.transit_server.public.table_group_1.v1"}