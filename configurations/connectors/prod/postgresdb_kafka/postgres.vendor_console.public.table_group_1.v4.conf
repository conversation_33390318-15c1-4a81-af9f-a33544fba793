{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.appointment,public.appointment_capacity_config,public.appointment_capacity_config_log,public.appointment_facility_day_capacity,public.appointment_facility_slot,public.appointment_facility_slot_log,public.appointment_log,public.appointment_po_mapping,public.appointment_slot_config,public.appointment_slot_config_log,public.appointment_slot_mapping,public.appointment_slot_mapping_log,public.auth_plan,public.base_bulk_upload_request_tracker,public.client_po_details,public.client_po_items,public.cms_assortment_request,public.cms_assortment_request_log,public.courier_partner_days_config,public.courier_partner_details,public.day_capacity_request_data_tracker,public.entity,public.entity_plan_mapping,public.facility_config,public.facility_config_log,public.facility_day_level_capacity,public.facility_day_level_capacity_log,public.facility_slot_time,public.facility_slot_time_log,public.invoice,public.invoice_event_log,public.invoice_payment_details,public.invoice_payment_mapping,public.item_proxy_category,public.po_reservation,public.po_reservation_log,public.report_requests,public.reservation_slot_details,public.reservation_slot_details_log,public.shipment,public.slot_capacity,public.slot_capacity_config,public.slot_capacity_log,public.slot_capacity_request_data_tracker,public.user_details,public.user_entity_mapping,public.vendor_facility_auto_release", "message.key.columns": "public.appointment:appointment_id;public.appointment_capacity_config:id;public.appointment_capacity_config_log:id;public.appointment_facility_day_capacity:id;public.appointment_facility_slot:id;public.appointment_facility_slot_log:id;public.appointment_log:id;public.appointment_po_mapping:id;public.appointment_slot_config:id;public.appointment_slot_config_log:id;public.appointment_slot_mapping:id;public.appointment_slot_mapping_log:id;public.auth_plan:id;public.base_bulk_upload_request_tracker:id;public.client_po_details:po_number;public.client_po_items:id;public.cms_assortment_request:id;public.cms_assortment_request_log:id;public.courier_partner_days_config:id;public.courier_partner_details:id;public.day_capacity_request_data_tracker:id;public.entity:id;public.entity_plan_mapping:id;public.facility_config:id;public.facility_config_log:id;public.facility_day_level_capacity:id;public.facility_day_level_capacity_log:id;public.facility_slot_time:id;public.facility_slot_time_log:id;public.invoice:id;public.invoice_event_log:id;public.invoice_payment_details:id;public.invoice_payment_mapping:id;public.item_proxy_category:id;public.po_reservation:id;public.po_reservation_log:id;public.report_requests:id;public.reservation_slot_details:id;public.reservation_slot_details_log:id;public.shipment:id;public.slot_capacity:id;public.slot_capacity_config:id;public.slot_capacity_log:id;public.slot_capacity_request_data_tracker:id;public.user_details:id;public.user_entity_mapping:id;public.vendor_facility_auto_release:id", "name": "postgres.vendor_console.public.table_group_1.v4", "snapshot.mode": "never", "database.server.name": "postgres.vendor_console", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "vendor_console", "slot.drop.on.stop": "false", "slot.name": "postgres_vendor_console_public_table_group_1_v4", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.vendor_console.public.table_group_1.v4"}