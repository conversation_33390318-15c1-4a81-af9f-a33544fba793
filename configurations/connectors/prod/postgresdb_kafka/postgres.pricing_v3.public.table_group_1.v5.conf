{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.attribute_management_allocationgrouprulemap,public.attribute_management_attributemasterrule,public.attribute_management_brandsfundallocationgroup,public.attribute_management_brandsfundapproval,public.attribute_management_brandsfundcityapproval,public.attribute_management_brandsfundsummary,public.attribute_management_brandssheetfile,public.attribute_management_itembrandfundagendaattribute,public.attribute_management_kvibrandfundagendaattribute,public.attribute_management_manufactureremail,public.bundles_and_combos_approval,public.bundles_and_combos_city_approval,public.bundles_and_combos_domain_bundlebrandfundsheetfile,public.catalog_domain_kvi_item,public.catalog_domain_kvitagging,public.competitor_domain_competitorconfiguration,public.competitor_domain_competitormaster,public.cron_tasks,public.event_management_domain_pricingeventerrors([_](default|p\\d{4}[_](0[1-9]|1[0-2])[_](0[1-9]|[12][0-9]|3[01]))),public.pricing_domain_city,public.pricing_domain_citycluster,public.pricing_domain_cmscity,public.pricing_domain_outlet,public.pricing_domain_prices,public.pricing_domain_product,public.pricing_domain_productchangelog([_](default|p\\d{4}[_](0[1-9]|1[0-2])[_](0[1-9]|[12][0-9]|3[01]))),public.pricing_domain_productchangelog([_](new1[_]default|new1[_]p\\d{4}[_](0[1-9]|1[0-2])[_](0[1-9]|[12][0-9]|3[01]))),public.pricing_domain_superstore,public.replication_domain_itemcityreplicationchangelog,public.rule_management_kviinternalbenchmarkingladder,public.rule_management_masterrule,public.rule_management_sheetfile,public.rule_management_agendarulerm,public.rule_management_agendarulekvialignedrm,public.rule_management_agendarulekviinternal,public.rule_management_agendarulekvirm,public.rule_management_agendarulermitemid,public.rule_management_pricingconfiguration,public.rule_management_agendarulesku,public.pricing_domain_pricerecommendation,public.rule_management_promotiontags,public.rule_management_pricingconfigurationsheetfile", "message.key.columns": "public.attribute_management_allocationgrouprulemap:id;public.attribute_management_attributemasterrule:id;public.attribute_management_brandsfundallocationgroup:id;public.attribute_management_brandsfundapproval:id;public.attribute_management_brandsfundcityapproval:id;public.attribute_management_brandsfundsummary:id;public.attribute_management_brandssheetfile:id;public.attribute_management_itembrandfundagendaattribute:id;public.attribute_management_kvibrandfundagendaattribute:id;public.attribute_management_manufactureremail:id;public.bundles_and_combos_approval:id;public.bundles_and_combos_city_approval:id;public.bundles_and_combos_domain_bundlebrandfundsheetfile:id;public.catalog_domain_kvi_item:id;public.catalog_domain_kvitagging:id;public.competitor_domain_competitorconfiguration:id;public.competitor_domain_competitormaster:id;public.cron_tasks:id;public.event_management_domain_pricingeventerrors:id;public.pricing_domain_city:id;public.pricing_domain_citycluster:id;public.pricing_domain_cmscity:id;public.pricing_domain_outlet:id;public.pricing_domain_prices:id;public.pricing_domain_product:id;public.pricing_domain_productchangelog:id;public.pricing_domain_superstore:id;public.replication_domain_itemcityreplicationchangelog:id;public.rule_management_agendarulekvialignedrm:id;public.rule_management_agendarulekviinternal:id;public.rule_management_agendarulekvirm:id;public.rule_management_agendarulerm:id;public.rule_management_agendarulermitemid:id;public.rule_management_agendarulesku:id;public.rule_management_kviinternalbenchmarkingladder:id;public.rule_management_masterrule:id;public.rule_management_pricingconfiguration:id;public.rule_management_sheetfile:id;public.pricing_domain_pricerecommendation:id;public.rule_management_promotiontags:id;public.rule_management_pricingconfigurationsheetfile:id", "name": "postgres.pricing_v3.public.table_group_1.v5", "snapshot.mode": "never", "database.server.name": "postgres.pricing_v3", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "pricing_v3", "transforms": "reroute_pricing_domain_productchangelog,reroute_event_management_domain_pricingeventerrors,reroute_pricing_domain_productchangelog_new1", "transforms.reroute_pricing_domain_productchangelog.type": "io.debezium.transforms.ByLogicalTableRouter", "transforms.reroute_pricing_domain_productchangelog.topic.regex": "(.*)pricing_domain_productchangelog([_](default|p\\d{4}[_](0[1-9]|1[0-2])[_](0[1-9]|[12][0-9]|3[01])))$", "transforms.reroute_pricing_domain_productchangelog.topic.replacement": "$1pricing_domain_productchangelog", "transforms.reroute_pricing_domain_productchangelog.key.enforce.uniqueness": "false", "transforms.reroute_pricing_domain_productchangelog_new1.type": "io.debezium.transforms.ByLogicalTableRouter", "transforms.reroute_pricing_domain_productchangelog_new1.topic.regex": "(.*)pricing_domain_productchangelog([_](new1[_]default|new1[_]p\\d{4}[_](0[1-9]|1[0-2])[_](0[1-9]|[12][0-9]|3[01])))$", "transforms.reroute_pricing_domain_productchangelog_new1.topic.replacement": "$1pricing_domain_productchangelog", "transforms.reroute_pricing_domain_productchangelog_new1.key.enforce.uniqueness": "false", "transforms.reroute_event_management_domain_pricingeventerrors.type": "io.debezium.transforms.ByLogicalTableRouter", "transforms.reroute_event_management_domain_pricingeventerrors.topic.regex": "(.*)event_management_domain_pricingeventerrors([_](default|p\\d{4}[_](0[1-9]|1[0-2])[_](0[1-9]|[12][0-9]|3[01])))$", "transforms.reroute_event_management_domain_pricingeventerrors.topic.replacement": "$1event_management_domain_pricingeventerrors", "transforms.reroute_event_management_domain_pricingeventerrors.key.enforce.uniqueness": "false", "slot.drop.on.stop": "false", "slot.name": "postgres_pricing_v3_public_table_group_1_v5", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "tombstones.on.delete": "false", "slot.max.retries": "20"}, "name": "postgres.pricing_v3.public.table_group_1.v5"}