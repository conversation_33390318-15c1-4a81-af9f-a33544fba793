{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.access_groups,public.departments,public.designations,public.enrollments,public.rosters,public.user_access_group_mappings,public.user_documents,public.user_properties,public.user_rosters,public.users", "message.key.columns": "public.access_groups:id;public.departments:id;public.designations:id;public.enrollments:id;public.rosters:id;public.user_access_group_mappings:id;public.user_documents:id;public.user_properties:id;public.user_rosters:id;public.users:id", "name": "postgres.user_management.public.table_group_1.v1", "snapshot.mode": "exported", "database.server.name": "postgres.user_management", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "user_management", "slot.drop.on.stop": "false", "slot.name": "postgres_user_management_public_table_group_1_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.user_management.public.table_group_1.v1"}