{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.entity_rules,public.gr_attribute,public.gr_attribute_option_value,public.gr_brand,public.gr_brand_category_mapping,public.gr_company,public.gr_coordinate,public.gr_group,public.gr_group_attribute_mapping,public.gr_locality,public.gr_merchant,public.gr_merchant_additional_info,public.gr_merchant_category_mapping,public.gr_merchant_chain,public.gr_merchant_finance_info,public.gr_tag,public.gr_user,public.gr_user_merchant,public.gr_user_role_mapping,public.gr_virtual_to_real_merchant_mapping", "message.key.columns": "public.entity_rules:id;public.gr_attribute:id;public.gr_attribute_option_value:id;public.gr_brand:id;public.gr_brand_category_mapping:id;public.gr_company:id;public.gr_coordinate:id;public.gr_group:id;public.gr_group_attribute_mapping:id;public.gr_locality:id;public.gr_merchant:id;public.gr_merchant_additional_info:id;public.gr_merchant_category_mapping:id;public.gr_merchant_chain:id;public.gr_merchant_finance_info:id;public.gr_tag:id;public.gr_user:id;public.gr_user_merchant:id;public.gr_user_role_mapping:id;public.gr_virtual_to_real_merchant_mapping:id", "name": "postgres.cms.public.table_group_1.v3", "snapshot.mode": "never", "database.server.name": "postgres.cms", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "cms", "slot.drop.on.stop": "false", "slot.name": "postgres_cms_public_table_group_1_v3", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.cms.public.table_group_1.v3"}