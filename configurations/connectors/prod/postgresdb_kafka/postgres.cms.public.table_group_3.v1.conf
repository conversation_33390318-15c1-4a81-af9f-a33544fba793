{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.attribute_template,public.attribute_template_constituent_attribute_mapping,public.entity,public.entity_requests", "message.key.columns": "public.attribute_template:id;public.attribute_template_constituent_attribute_mapping:id;public.entity:id;public.entity_requests:id", "name": "postgres.cms.public.table_group_3.v1", "snapshot.mode": "exported", "database.server.name": "postgres.cms", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "cms", "slot.drop.on.stop": "false", "slot.name": "postgres_cms_public_table_group_3_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.cms.public.table_group_3.v1"}