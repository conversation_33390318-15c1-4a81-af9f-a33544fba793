{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.personalisation_story_metadata,public.story_information,public.master_reranking_data_v2", "message.key.columns": "public.personalisation_story_metadata:id;public.story_information:id;public.master_reranking_data_v2:id", "name": "postgres.dse_db.public.snapshot20250818.v1", "snapshot.mode": "exported", "database.server.name": "postgres.dse_db", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "dse_db", "slot.drop.on.stop": "false", "slot.name": "postgres_dse_db_public_snapshot20250818_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.dse_db.public.snapshot20250818.v1"}