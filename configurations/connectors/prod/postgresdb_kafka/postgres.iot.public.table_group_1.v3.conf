{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.tracking_device,public.tracking_device_entity_mapping,public.tracking_entity,public.tracking_event([_](default|p\\d{4}[_](0[1-9]|1[0-2]))),public.tracking_event_new([_](default|p\\d{4}[_](0[1-9]|1[0-2]))),public.tracking_event_details,public.notification,public.notification_condition,public.notification_log,public.notification_mapping", "message.key.columns": "public.notification:id;public.notification_condition:id;public.notification_log:id;public.notification_mapping:id;public.tracking_device:id;public.tracking_device_entity_mapping:id;public.tracking_entity:id;public.tracking_event:id;public.tracking_event_details:id", "name": "postgres.iot.public.table_group_1.v3", "snapshot.mode": "never", "database.server.name": "postgres.iot", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "iot", "slot.drop.on.stop": "false", "transforms": "Reroute_tracking_event_task,Reroute_tracking_event_task_new", "transforms.Reroute_tracking_event_task.type": "io.debezium.transforms.ByLogicalTableRouter", "transforms.Reroute_tracking_event_task.topic.regex": "(.*)tracking_event([_](default|p\\d{4}[_](0[1-9]|1[0-2])))$", "transforms.Reroute_tracking_event_task.topic.replacement": "$1tracking_event", "transforms.Reroute_tracking_event_task.key.enforce.uniqueness": "false", "transforms.Reroute_tracking_event_task_new.type": "io.debezium.transforms.ByLogicalTableRouter", "transforms.Reroute_tracking_event_task_new.topic.regex": "(.*)tracking_event_new([_](default|p\\d{4}[_](0[1-9]|1[0-2])))$", "transforms.Reroute_tracking_event_task_new.topic.replacement": "$1tracking_event", "transforms.Reroute_tracking_event_task_new.key.enforce.uniqueness": "false", "slot.name": "postgres_iot_public_table_group_1_v3", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.iot.public.table_group_1.v3"}