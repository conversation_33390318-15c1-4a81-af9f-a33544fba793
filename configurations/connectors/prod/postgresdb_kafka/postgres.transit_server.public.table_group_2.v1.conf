{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.transit_allocation_fulfillment_queue,public.transit_courier_order,public.transit_courier_order_log,public.transit_delivery_challan,public.transit_delivery_challan_item,public.transit_discrepancy_notification_mapping,public.transit_ewaybill,public.transit_metrics,public.transit_metrics_group,public.transit_metrics_group_v2,public.transit_metrics_v2,public.transit_node_mapping,public.transit_notification,public.transit_notification_user_mapping,public.transit_projection_dispatch_demand,public.transit_projection_plan_vehicle,public.transit_projection_plan_vehicle_destination,public.transit_projection_trip_duration,public.transit_task_node_detail,public.transit_travel_segment,public.transit_user_group,public.transit_user_group_view_mapping,public.transit_user_profile_group,public.transit_user_session", "message.key.columns": "public.transit_allocation_fulfillment_queue:id;public.transit_courier_order:id;public.transit_courier_order_log:id;public.transit_delivery_challan:id;public.transit_delivery_challan_item:id;public.transit_discrepancy_notification_mapping:id;public.transit_ewaybill:invoice_id;public.transit_metrics:id;public.transit_metrics_group:id;public.transit_metrics_group_v2:id;public.transit_metrics_v2:id;public.transit_node_mapping:id;public.transit_notification:id;public.transit_notification_user_mapping:id;public.transit_projection_dispatch_demand:id;public.transit_projection_plan_vehicle:id;public.transit_projection_plan_vehicle_destination:id;public.transit_projection_trip_duration:id;public.transit_task_node_detail:id;public.transit_travel_segment:id;public.transit_user_group:id;public.transit_user_group_view_mapping:id;public.transit_user_profile_group:id;public.transit_user_session:id", "name": "postgres.transit_server.public.table_group_2.v1", "snapshot.mode": "exported", "database.server.name": "postgres.transit_server", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "transit_server", "slot.drop.on.stop": "false", "slot.name": "postgres_transit_server_public_table_group_2_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.transit_server.public.table_group_2.v1"}