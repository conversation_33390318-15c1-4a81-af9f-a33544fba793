{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.dashboards,public.data_source_groups,public.data_sources,public.destinations,public.groups,public.users,public.visualizations,public.widgets", "message.key.columns": "public.dashboards:id;public.data_source_groups:id;public.data_sources:id;public.destinations:id;public.groups:id;public.users:id;public.visualizations:id;public.widgets:id", "name": "postgres.redash_reports.public.table_group_1.v7", "snapshot.mode": "never", "database.server.name": "postgres.redash_reports", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "redash_reports", "slot.drop.on.stop": "false", "slot.name": "postgres_redash_reports_public_table_group_1_v7", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "producer.override.max.request.size": "2097152", "slot.max.retries": "20", "tombstones.on.delete": "false"}, "name": "postgres.redash_reports.public.table_group_1.v7"}