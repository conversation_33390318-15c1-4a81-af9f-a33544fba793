{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.advertiser_transactions,public.ba_membership,public.campaign_history,public.feature_rollout,public.slot_booking,public.slot_projection,public.story", "message.key.columns": "public.advertiser_transactions:id;public.ba_membership:id;public.campaign_history:id;public.feature_rollout:id;public.slot_booking:id;public.slot_projection:id;public.story:id", "name": "postgres.adserver.public.table_group_2.v1", "snapshot.mode": "exported", "database.server.name": "postgres.adserver", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "adserver", "slot.drop.on.stop": "false", "slot.name": "postgres_adserver_public_table_group_2_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.adserver.public.table_group_2.v1"}