{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.applied_business_config,public.business_config_template,public.employee_tenant_map,public.employee_warnings,public.external_event,public.job_application,public.job_vacancy,public.leaderboard_ledger,public.leaderboard_projection,public.leave_actions,public.leave_balance,public.leaves,public.referral_leads,public.selfie_verification_attempts,public.training,public.training_challenge,public.training_component,public.training_component_translation,public.training_level,public.training_level_mapping,public.training_user_activity,public.training_user_progress,public.user_bank_details,public.user_milestone,public.user_skill,public.user_zone", "message.key.columns": "public.applied_business_config:id;public.business_config_template:id;public.employee_tenant_map:id;public.employee_warnings:id;public.external_event:id;public.job_application:id;public.job_vacancy:id;public.leaderboard_ledger:id;public.leaderboard_projection:id;public.leave_actions:id;public.leave_balance:id;public.leaves:id;public.referral_leads:id;public.selfie_verification_attempts:id;public.training:id;public.training_challenge:id;public.training_component:id;public.training_component_translation:id;public.training_level:id;public.training_level_mapping:id;public.training_user_activity:id;public.training_user_progress:id;public.user_bank_details:id;public.user_milestone:id;public.user_skill:id;public.user_zone:id", "name": "postgres.ops_management.public.table_group_2.v2", "snapshot.mode": "never", "database.server.name": "postgres.ops_management", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "ops_management", "slot.drop.on.stop": "false", "slot.name": "postgres_ops_management_public_table_group_2_v2", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.ops_management.public.table_group_2.v2"}