{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.auth_user,public.digitisation_invoice_mapping_v2,public.digitisation_request,public.digitisation_request_action,public.digitise_bulk_invoice_request,public.digitise_invoice_request,public.digitised_document,public.digitised_document_log,public.digitised_invoice_data,public.digitised_invoice_item_data,public.discrepancy_note_data,public.discrepancy_note_data_dump,public.grn_data,public.invoice_verification_request,public.purchase_item_data,public.purchase_order_data,public.seller,public.tenant,public.user_management_user_group,public.user_seller_map,public.user_tenant_map,public.verification_request,public.verification_request_step,public.verification_step,public.verification_trail,public.verified_invoice_data,public.verified_invoice_data_log,public.verified_invoice_items_data,public.verified_invoice_items_data_log", "message.key.columns": "public.auth_user:id;public.digitisation_invoice_mapping_v2:id;public.digitisation_request:id;public.digitisation_request_action:id;public.digitise_bulk_invoice_request:id;public.digitise_invoice_request:id;public.digitised_document:id;public.digitised_document_log:id;public.digitised_invoice_data:id;public.digitised_invoice_item_data:id;public.discrepancy_note_data:id;public.discrepancy_note_data_dump:id;public.grn_data:id;public.invoice_verification_request:id;public.purchase_item_data:id;public.purchase_order_data:id;public.seller:id;public.tenant:id;public.user_management_user_group:id;public.user_seller_map:id;public.user_tenant_map:id;public.verification_request:id;public.verification_request_step:id;public.verification_step:id;public.verification_trail:id;public.verified_invoice_data:id;public.verified_invoice_data_log:id;public.verified_invoice_items_data:id;public.verified_invoice_items_data_log:id", "name": "postgres.document_digitisation.public.table_group_1.v4", "snapshot.mode": "never", "database.server.name": "postgres.document_digitisation", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "document_digitisation", "slot.drop.on.stop": "false", "slot.name": "postgres_document_digitisation_public_table_group_1_v4", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "producer.override.max.request.size": "2097152", "slot.max.retries": "20", "column.exclude.list": "public.auth_user.password"}, "name": "postgres.document_digitisation.public.table_group_1.v4"}