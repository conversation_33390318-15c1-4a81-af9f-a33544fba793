{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.dag,public.dag_run,public.serialized_dags,public.slot_pool,public.task_instance", "message.key.columns": "public.dag:dag_id;public.dag_run:id;public.serialized_dags:dag_id;public.slot_pool:id;public.task_instance:task_id,dag_id,run_id,map_index", "name": "postgres.airflow.public.table_group_1.v1", "snapshot.mode": "exported", "database.server.name": "postgres.airflow", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "airflow", "slot.drop.on.stop": "false", "slot.name": "postgres_airflow_public_table_group_1_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.airflow.public.table_group_1.v1"}