{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.agent_handover_request,public.crm_agent_profile,public.crm_agent_resolution,public.crm_call_logs_entry,public.crm_chat_media,public.crm_dashboard_permission,public.crm_device_block_action_history,public.crm_force_update_karma,public.crm_helpdesk_node,public.crm_helpdesk_rule,public.crm_issue,public.crm_issue_item,public.crm_issue_type,public.crm_order_tag,public.crm_order_task,public.crm_permission_update_event,public.crm_refund,public.crm_self_service_action,public.crm_source,public.crm_tag,public.crm_user,public.crm_user_permission,public.delight_money_transaction,public.user_abuse_details", "message.key.columns": "public.agent_handover_request:id;public.crm_agent_profile:id;public.crm_agent_resolution:id;public.crm_call_logs_entry:id;public.crm_chat_media:id;public.crm_dashboard_permission:id;public.crm_device_block_action_history:id;public.crm_force_update_karma:id;public.crm_helpdesk_node:id;public.crm_helpdesk_rule:id;public.crm_issue:id;public.crm_issue_item:id;public.crm_issue_type:id;public.crm_order_tag:id;public.crm_order_task:id;public.crm_permission_update_event:id;public.crm_refund:id;public.crm_self_service_action:id;public.crm_source:id;public.crm_tag:id;public.crm_user:id;public.crm_user_permission:id;public.delight_money_transaction:id;public.user_abuse_details:id", "name": "postgres.crm.public.table_group_1.v5", "snapshot.mode": "never", "database.server.name": "postgres.crm", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "crm", "slot.drop.on.stop": "false", "slot.name": "postgres_crm_public_table_group_1_v5", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.crm.public.table_group_1.v5"}