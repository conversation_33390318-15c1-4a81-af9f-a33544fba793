{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.layout_change_request_log,public.layout_component,public.legend_mapping_detail,public.outlet_floor_path_version,public.outlet_floor_shortest_path,public.outlet_layout", "message.key.columns": "public.layout_change_request_log:id;public.layout_component:id;public.legend_mapping_detail:id;public.outlet_floor_path_version:id;public.outlet_floor_shortest_path:id;public.outlet_layout:id", "name": "postgres.inventory_lm.public.table_group_1.v1", "snapshot.mode": "exported", "database.server.name": "postgres.inventory_lm", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "inventory_lm", "slot.drop.on.stop": "false", "slot.name": "postgres_inventory_lm_public_table_group_1_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "producer.override.max.request.size": "72280272", "slot.max.retries": "20"}, "name": "postgres.inventory_lm.public.table_group_1.v1"}