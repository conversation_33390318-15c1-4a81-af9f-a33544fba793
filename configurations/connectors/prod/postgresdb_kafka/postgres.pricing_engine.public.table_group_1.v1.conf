{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.brand_fund_rule,public.cluster,public.cluster_store_mapping,public.competitor_config,public.competitor_rule,public.config,public.master_rule,public.offer_product_id_constituents,public.price,public.pricing_configuration,public.rule_approval,public.sheet_files,public.store", "message.key.columns": "public.brand_fund_rule:id;public.cluster:id;public.cluster_store_mapping:cluster_id,store_id;public.competitor_config:id;public.competitor_rule:id;public.config:id;public.master_rule:id;public.offer_product_id_constituents:id;public.price:id;public.pricing_configuration:id;public.rule_approval:id;public.sheet_files:id;public.store:id", "name": "postgres.pricing_engine.public.table_group_1.v1", "snapshot.mode": "exported", "database.server.name": "postgres.pricing_engine", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "pricing_engine", "slot.drop.on.stop": "false", "slot.name": "postgres_pricing_engine_public_table_group_1_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.pricing_engine.public.table_group_1.v1"}