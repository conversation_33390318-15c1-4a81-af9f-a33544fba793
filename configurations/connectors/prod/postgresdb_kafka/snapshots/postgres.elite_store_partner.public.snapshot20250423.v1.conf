{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.store", "message.key.columns": "public.store:id", "name": "postgres.elite_store_partner.public.snapshot20250423.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.elite_store_partner", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "elite_store_partner", "slot.drop.on.stop": "true", "slot.name": "postgres_elite_store_partner_public_snapshot20250423_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.elite_store_partner.public.snapshot20250423.v1"}