{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.notification,public.notification_condition,public.notification_log,public.notification_mapping", "message.key.columns": "public.notification:id;public.notification_condition:id;public.notification_log:id;public.notification_mapping:id", "name": "postgres.iot.public.snapshot20250523.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.iot", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "iot", "slot.drop.on.stop": "true", "slot.name": "postgres_iot_public_snapshot20250523_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.iot.public.snapshot20250523.v1"}