{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.slot_booking,public.slot_projection", "message.key.columns": "public.slot_booking:id;public.slot_projection:id", "name": "postgres.adserver.public.snapshot20250715.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.adserver", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "adserver", "slot.drop.on.stop": "true", "slot.name": "postgres_adserver_public_snapshot20250715_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.adserver.public.snapshot20250715.v1"}