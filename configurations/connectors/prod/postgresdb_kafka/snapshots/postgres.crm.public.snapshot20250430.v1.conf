{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.crm_order_task", "message.key.columns": "public.crm_order_task:id", "name": "postgres.crm.public.snapshot20250430.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.crm", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "crm", "slot.drop.on.stop": "true", "slot.name": "postgres_crm_public_snapshot_20250430_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.crm.public.snapshot20250430.v1"}