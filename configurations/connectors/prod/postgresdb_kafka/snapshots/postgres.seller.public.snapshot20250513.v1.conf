{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.campaigns,public.scheduled_jobs,public.cart,public.cart_transaction_log,public.cart_order_mapping", "message.key.columns": "public.campaigns:id;public.scheduled_jobs:id;public.cart:id;public.cart_transaction_log:id;public.cart_order_mapping:id", "name": "postgres.seller.public.snapshot20250513.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.seller", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "seller", "slot.drop.on.stop": "true", "slot.name": "postgres_seller_public_snapshot20250513_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.seller.public.snapshot20250513.v1"}