{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.entity,public.entity_schedule", "message.key.columns": "public.entity:id;public.entity_schedule:id", "name": "postgres.kitchen.public.snapshot20250513.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.kitchen", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "kitchen", "slot.drop.on.stop": "true", "slot.name": "postgres_kitchen_public_snapshot20250513_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.kitchen.public.snapshot20250513.v1"}