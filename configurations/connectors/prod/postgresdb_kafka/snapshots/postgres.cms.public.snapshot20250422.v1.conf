{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.gr_merchant_product_mapping", "message.key.columns": "public.gr_merchant_product_mapping:id", "name": "postgres.cms.public.snapshot20250422.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.cms", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "cms", "slot.drop.on.stop": "false", "slot.name": "postgres_cms_public_snapshot20250422_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.cms.public.snapshot20250422.v1"}