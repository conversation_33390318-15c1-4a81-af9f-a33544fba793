{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.user_access_group_mappings", "message.key.columns": "public.user_access_group_mappings:id", "name": "postgres.user_management.public.snapshot20250513.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.user_management", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "user_management", "slot.drop.on.stop": "true", "slot.name": "postgres_user_management_public_snapshot20250513_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.user_management.public.snapshot20250513.v1"}