{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.workflow", "message.key.columns": "public.workflow:id", "name": "postgres.seller.public.snapshot20250612.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.seller", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "seller", "slot.drop.on.stop": "true", "slot.name": "postgres_seller_public_snapshot20250612_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.seller.public.snapshot20250612.v1"}