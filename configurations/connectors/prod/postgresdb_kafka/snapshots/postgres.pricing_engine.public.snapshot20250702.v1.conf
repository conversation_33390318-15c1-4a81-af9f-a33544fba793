{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.cluster,public.cluster_store_mapping,public.competitor_config,public.config,public.rule_approval,public.sheet_files,public.store", "message.key.columns": "public.cluster:id;public.cluster_store_mapping:cluster_id,store_id;public.competitor_config:id;public.config:id;public.rule_approval:id;public.sheet_files:id;public.store:id", "name": "postgres.pricing_engine.public.snapshot20250702.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.pricing_engine", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "pricing_engine", "slot.drop.on.stop": "true", "slot.name": "postgres_pricing_engine_public_snapshot_20250702_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.pricing_engine.public.snapshot20250702.v1"}