{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.po_reservation,public.po_reservation_log,public.reservation_slot_details,public.reservation_slot_details_log,public.vendor_facility_auto_release", "message.key.columns": "public.po_reservation:id;public.po_reservation_log:id;public.reservation_slot_details:id;public.reservation_slot_details_log:id;public.vendor_facility_auto_release:id", "name": "postgres.vendor_console.public.snapshot20250430.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.vendor_console", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "vendor_console", "slot.drop.on.stop": "true", "slot.name": "postgres_vendor_console_public_snapshot_20250430_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.vendor_console.public.snapshot20250430.v1"}