{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.location_migration_mapping", "message.key.columns": "public.location_migration_mapping:id", "name": "postgres.location.public.snapshot20250306.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.location", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "location", "slot.drop.on.stop": "true", "slot.name": "postgres_location_public_snapshot20250306_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.location.public.snapshot20250306.v1"}