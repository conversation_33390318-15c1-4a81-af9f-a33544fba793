{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.complaints_rca", "message.key.columns": "public.complaints_rca:id", "name": "postgres.kitchen.public.snapshot20250827.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.kitchen", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "kitchen", "slot.drop.on.stop": "true", "slot.name": "postgres_kitchen_public_snapshot20250827_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.kitchen.public.snapshot20250827.v1"}