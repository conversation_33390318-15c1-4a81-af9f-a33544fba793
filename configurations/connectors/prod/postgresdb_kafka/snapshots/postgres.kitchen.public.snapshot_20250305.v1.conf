{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.order_states", "message.key.columns": "public.order_states:id", "name": "postgres.kitchen.public.snapshot_20250305.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.kitchen", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "kitchen", "slot.drop.on.stop": "true", "slot.name": "postgres_kitchen_public_snapshot_20250305_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.kitchen.public.snapshot_20250305.v1"}