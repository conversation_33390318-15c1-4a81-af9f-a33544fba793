{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.city_info", "message.key.columns": "public.city_info:id", "name": "postgres.search_ranking.public.snapshot_20250416.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.search_ranking", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "search_ranking", "slot.drop.on.stop": "true", "slot.name": "postgres_search_ranking_public_snapshot_v1_20250416", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.search_ranking.public.snapshot_20250416.v1"}