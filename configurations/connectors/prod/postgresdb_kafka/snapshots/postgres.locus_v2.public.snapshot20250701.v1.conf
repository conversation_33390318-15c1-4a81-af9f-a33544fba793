{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.document_classifications,public.document_form_mappings,public.document_records,public.form_final_status_lookup,public.project_final_status_lookup,public.sub_forms", "message.key.columns": "public.document_classifications:id;public.document_form_mappings:id;public.document_records:id;public.form_final_status_lookup:form_id,team_id,status_id;public.project_final_status_lookup:form_id;public.sub_forms:id", "name": "postgres.locus_v2.public.snapshot20250701.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.locus_v2", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "locus_v2", "slot.drop.on.stop": "true", "slot.name": "postgres_locus_v2_public_snapshot20250701_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.locus_v2.public.snapshot20250701.v1"}