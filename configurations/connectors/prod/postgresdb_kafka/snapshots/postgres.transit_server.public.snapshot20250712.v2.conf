{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.transit_consignment_container", "message.key.columns": "public.transit_consignment_container:id", "name": "postgres.transit_server.public.snapshot20250712.v2", "snapshot.mode": "initial_only", "database.server.name": "postgres.transit_server", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "transit_server", "slot.drop.on.stop": "true", "slot.name": "postgres_transit_server_public_snapshot20250712_v2", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20", "tombstones.on.delete": "false", "snapshot.select.statement.overrides": "public.transit_consignment_container", "snapshot.select.statement.overrides.public.transit_consignment_container": "select * from public.transit_consignment_container where id >= 182628689"}, "name": "postgres.transit_server.public.snapshot20250712.v2"}