{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.user_zone", "message.key.columns": "public.user_zone:id", "name": "postgres.ops_management.public.snapshot20250723.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.ops_management", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "ops_management", "slot.drop.on.stop": "true", "slot.name": "postgres_ops_management_public_snapshot20250723_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.ops_management.public.snapshot20250723.v1"}