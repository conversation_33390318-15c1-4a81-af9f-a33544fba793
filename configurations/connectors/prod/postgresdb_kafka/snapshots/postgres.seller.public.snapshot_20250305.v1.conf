{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.seller_document_info,public.user_seller_mapping", "message.key.columns": "public.seller_document_info:id;public.user_seller_mapping:id", "name": "postgres.seller.public.snapshot_20250305.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.seller", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "seller", "slot.drop.on.stop": "true", "slot.name": "postgres_seller_public_snapshot_20250305_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.seller.public.snapshot_20250305.v1"}