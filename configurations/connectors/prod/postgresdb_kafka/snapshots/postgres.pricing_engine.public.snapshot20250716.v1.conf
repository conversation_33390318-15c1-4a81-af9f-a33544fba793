{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.pricing_configuration", "message.key.columns": "public.pricing_configuration:id", "name": "postgres.pricing_engine.public.snapshot20250716.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.pricing_engine", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "pricing_engine", "slot.drop.on.stop": "true", "slot.name": "postgres_pricing_engine_public_snapshot20250716_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.pricing_engine.public.snapshot20250716.v1"}