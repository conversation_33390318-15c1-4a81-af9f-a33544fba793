{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.entity_rules", "message.key.columns": "public.entity_rules:id", "name": "postgres.cms.public.snapshot20250702.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.cms", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "cms", "slot.drop.on.stop": "true", "slot.name": "postgres_cms_public_snapshot20250702_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.cms.public.snapshot20250702.v1"}