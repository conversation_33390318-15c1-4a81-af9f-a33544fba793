{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.fleet_management_vehicle_type", "message.key.columns": "public.fleet_management_vehicle_type:id", "name": "postgres.fleet_management.public.snapshot20250825.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.fleet_management", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "fleet_management", "slot.drop.on.stop": "true", "slot.name": "postgres_fleet_management_public_snapshot20250825_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.fleet_management.public.snapshot20250825.v1"}