{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.employee_activity_tracker", "message.key.columns": "public.employee_activity_tracker:id", "name": "postgres.warehouse_metrics.public.snapshot20250610.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.warehouse_metrics", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "warehouse_metrics", "slot.drop.on.stop": "true", "slot.name": "postgres_warehouse_metrics_public_snapshot20250610_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.warehouse_metrics.public.snapshot20250610.v1"}