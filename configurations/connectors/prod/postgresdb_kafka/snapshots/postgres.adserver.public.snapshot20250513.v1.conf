{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.story", "message.key.columns": "public.story:id", "name": "postgres.adserver.public.snapshot20250513.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.adserver", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "adserver", "slot.drop.on.stop": "true", "slot.name": "postgres_adserver_public_snapshot20250513_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.adserver.public.snapshot20250513.v1"}