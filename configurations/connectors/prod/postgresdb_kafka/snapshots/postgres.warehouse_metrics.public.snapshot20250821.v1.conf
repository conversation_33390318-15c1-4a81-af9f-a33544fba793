{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.vehicle_registry", "message.key.columns": "public.vehicle_registry:id", "name": "postgres.warehouse_metrics.public.snapshot20250821.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.warehouse_metrics", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "warehouse_metrics", "slot.drop.on.stop": "true", "slot.name": "postgres_warehouse_metrics_public_snapshot20250821_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.warehouse_metrics.public.snapshot20250821.v1"}