{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.auth_user", "message.key.columns": "public.auth_user:id", "name": "postgres.document_digitisation.public.snapshot20250610.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.document_digitisation", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "document_digitisation", "slot.drop.on.stop": "true", "slot.name": "postgres_document_digitisation_public_snapshot20250610_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20", "column.exclude.list": "public.auth_user.password"}, "name": "postgres.document_digitisation.public.snapshot20250610.v1"}