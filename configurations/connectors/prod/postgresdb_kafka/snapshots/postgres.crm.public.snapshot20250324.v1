{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.crm_device_block_action_history", "message.key.columns": "public.crm_device_block_action_history:id", "name": "postgres.crm.public.snapshot20250324.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.crm", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "crm", "slot.drop.on.stop": "true", "slot.name": "postgres_crm_public_snapshot20250324_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.crm.public.snapshot20250324.v1"}