{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.outlet_floor_shortest_path", "message.key.columns": "public.outlet_floor_shortest_path:id", "name": "postgres.inventory_lm.public.snapshot20250513.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.inventory_lm", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "inventory_lm", "slot.drop.on.stop": "true", "slot.name": "postgres_inventory_lm_public_snapshot20250513_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.inventory_lm.public.snapshot20250513.v1"}