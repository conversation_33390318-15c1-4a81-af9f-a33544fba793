{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.recall_request,public.item_outlet_inventory,public.recall_request_batch,public.recall_request_batch_item,public.recall_request_batch_log,public.recall_request_batch_pro_detail", "message.key.columns": "public.recall_request:id;public.item_outlet_inventory:id;public.recall_request_batch:id;public.recall_request_batch_item:id;public.recall_request_batch_log:id;public.recall_request_batch_pro_detail:id", "name": "postgres.seller.public.snapshot20250630.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.seller", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "seller", "slot.drop.on.stop": "true", "slot.name": "postgres_seller_public_snapshot20250630_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.seller.public.snapshot20250630.v1"}