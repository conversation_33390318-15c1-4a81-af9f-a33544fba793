{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.patient_details,public.medical_staff", "message.key.columns": "public.patient_details:id,public.medical_staff:id", "name": "postgres.emergency_services.public.snapshot20250321.v1", "snapshot.mode": "never", "database.server.name": "postgres.emergency_services", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "emergency_services", "slot.drop.on.stop": "false", "slot.name": "postgres_emergency_services_public_snapshot20250321_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.emergency_services.public.snapshot20250321.v1"}