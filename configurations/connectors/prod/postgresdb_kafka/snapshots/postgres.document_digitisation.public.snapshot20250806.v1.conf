{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.digitise_bulk_invoice_request,public.digitise_invoice_request", "message.key.columns": "public.digitise_bulk_invoice_request:id;public.digitise_invoice_request:id", "name": "postgres.document_digitisation.public.snapshot20250806.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.document_digitisation", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "document_digitisation", "slot.drop.on.stop": "true", "slot.name": "postgres_document_digitisation_public_snapshot20250806_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.document_digitisation.public.snapshot20250806.v1"}