{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.ambulance,public.ambulance_trip_staff,public.depot", "message.key.columns": "public.ambulance:id;public.ambulance_trip_staff:id;public.depot:id", "name": "postgres.emergency_services.public.snapshot_20250416.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.emergency_services", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "emergency_services", "slot.drop.on.stop": "true", "slot.name": "postgres_emergency_services_public_snapshot_v1_20250416", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.emergency_services.public.snapshot_20250416.v1"}