{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.slot_capacity_config", "message.key.columns": "public.slot_capacity_config:id", "name": "postgres.vendor_console.public.snapshot20250723.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.vendor_console", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "vendor_console", "slot.drop.on.stop": "true", "slot.name": "postgres_vendor_console_public_snapshot20250723_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.vendor_console.public.snapshot20250723.v1"}