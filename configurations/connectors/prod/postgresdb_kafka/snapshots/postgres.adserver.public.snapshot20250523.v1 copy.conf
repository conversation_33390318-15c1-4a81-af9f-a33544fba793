{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.campaign_history", "message.key.columns": "public.campaign_history:id", "name": "postgres.adserver.public.snapshot20250523.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.adserver", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "adserver", "slot.drop.on.stop": "true", "slot.name": "postgres_adserver_public_snapshot20250523_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.adserver.public.snapshot20250523.v1"}