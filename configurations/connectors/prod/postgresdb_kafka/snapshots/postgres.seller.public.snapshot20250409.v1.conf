{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.task,public.workflow", "message.key.columns": "public.task:id;public.workflow:id", "column.exclude.list": "public.task.meta,public.workflow.meta", "name": "postgres.seller.public.snapshot20250409.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.seller", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "seller", "slot.drop.on.stop": "true", "slot.name": "postgres_seller_public_snapshot_20250409_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.seller.public.snapshot20250409.v1"}