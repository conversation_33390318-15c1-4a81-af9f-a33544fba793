{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.station_product_mapping,public.product_mapping", "message.key.columns": "public.station_product_mapping:id;public.product_mapping:id", "name": "postgres.kitchen.public.snapshot_20250416.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.kitchen", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "kitchen", "slot.drop.on.stop": "true", "slot.name": "postgres_kitchen_public_snapshot_v1_20250416", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.kitchen.public.snapshot_20250416.v1"}