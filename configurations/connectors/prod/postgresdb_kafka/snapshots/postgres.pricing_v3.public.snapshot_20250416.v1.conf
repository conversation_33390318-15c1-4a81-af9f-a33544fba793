{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.rule_management_pricingconfiguration", "message.key.columns": "public.rule_management_pricingconfiguration:id", "name": "postgres.pricing_v3.public.snapshot_20250416.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.pricing_v3", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "pricing_v3", "slot.drop.on.stop": "true", "slot.name": "postgres_pricing_v3_public_snapshot_v1_20250416", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.pricing_v3.public.snapshot_20250416.v1"}