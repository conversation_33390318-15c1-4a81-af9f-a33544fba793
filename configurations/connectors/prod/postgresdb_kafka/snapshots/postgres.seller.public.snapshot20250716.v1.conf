{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.seller_t_and_c", "message.key.columns": "public.seller_t_and_c:id", "name": "postgres.seller.public.snapshot20251716.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.seller", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "seller", "slot.drop.on.stop": "true", "slot.name": "postgres_seller_public_snapshot20251716_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.seller.public.snapshot20251716.v1"}