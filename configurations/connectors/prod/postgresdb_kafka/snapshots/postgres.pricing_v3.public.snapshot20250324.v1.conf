{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.cron_tasks,public.replication_domain_itemcityreplicationchangelog", "message.key.columns": "public.cron_tasks:id,public.replication_domain_itemcityreplicationchangelog:id", "name": "postgres.pricing_v3.snapshot_20250324.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.pricing_v3", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "pricing_v3", "slot.drop.on.stop": "true", "slot.name": "postgres_pricing_v3_snapshot_20250324_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.pricing_v3.snapshot_20250324.v1"}