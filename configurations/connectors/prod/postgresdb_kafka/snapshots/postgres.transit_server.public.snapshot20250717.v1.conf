{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.transit_metrics_v2,public.transit_metrics_group_v2,public.transit_courier_order,public.transit_courier_order_log", "message.key.columns": "public.transit_metrics_v2:id;public.transit_metrics_group_v2:id;public.transit_courier_order:id;public.transit_courier_order_log:id", "name": "postgres.transit_server.public.snapshot20250717.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.transit_server", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "transit_server", "slot.drop.on.stop": "true", "slot.name": "postgres_transit_server_public_snapshot20250717_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.transit_server.public.snapshot20250717.v1"}