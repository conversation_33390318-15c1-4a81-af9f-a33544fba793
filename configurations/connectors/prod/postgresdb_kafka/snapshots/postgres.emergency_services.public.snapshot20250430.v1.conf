{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.ambulance_trip,public.ambulance_trip_event,public.ambulance_order_trip_mapping", "message.key.columns": "public.ambulance_trip:id;public.ambulance_trip_event:id;public.ambulance_order_trip_mapping:id", "name": "postgres.emergency_services.public.snapshot20250430.v1", "snapshot.mode": "initial_only", "database.server.name": "postgres.emergency_services", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "emergency_services", "slot.drop.on.stop": "true", "slot.name": "postgres_emergency_services_public_snapshot_20250430_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.emergency_services.public.snapshot20250430.v1"}