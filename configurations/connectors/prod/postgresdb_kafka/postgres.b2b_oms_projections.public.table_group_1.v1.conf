{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.consignment_lifecycle,public.consignment_lifecycle_event_log", "message.key.columns": "public.consignment_lifecycle:consignment_id;public.consignment_lifecycle_event_log:event_id", "name": "postgres.b2b_oms_projections.public.table_group_1.v1", "snapshot.mode": "exported", "database.server.name": "postgres.b2b_oms_projections", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "b2b_oms_projections", "slot.drop.on.stop": "false", "slot.name": "postgres_b2b_oms_projections_public_table_group_1_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "producer.override.max.request.size": "20670360", "slot.max.retries": "20"}, "name": "postgres.b2b_oms_projections.public.table_group_1.v1"}