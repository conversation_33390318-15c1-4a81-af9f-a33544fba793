{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.dashboard_user,public.keyword_metrics,public.query_action,public.suggestion,public.suggestion_value", "message.key.columns": "public.dashboard_user:id;public.keyword_metrics:id;public.query_action:id;public.suggestion:id;public.suggestion_value:id", "name": "postgres.search_management.public.table_group_1.v1", "snapshot.mode": "exported", "database.server.name": "postgres.search_management", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "search_management", "slot.drop.on.stop": "false", "slot.name": "postgres_search_management_public_table_group_1_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.search_management.public.table_group_1.v1"}