{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.faq", "message.key.columns": "public.faq:id", "name": "postgres.seller.public.seller.v1", "snapshot.mode": "exported", "database.server.name": "postgres.seller", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "seller", "slot.drop.on.stop": "false", "slot.name": "postgres_seller_public_seller_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20", "column.exclude.list": "public.task.meta,public.workflow.meta"}, "name": "postgres.seller.public.seller.v1"}