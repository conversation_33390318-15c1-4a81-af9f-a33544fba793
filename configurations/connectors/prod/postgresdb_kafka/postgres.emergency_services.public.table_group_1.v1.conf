{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.ambulance,public.ambulance_checklist_item_update,public.ambulance_checklist_session,public.ambulance_order_trip_mapping,public.ambulance_staff_evaluation,public.ambulance_trip,public.ambulance_trip_event,public.ambulance_trip_staff,public.consumable,public.consumable_batch,public.depot,public.medical_remark,public.medical_staff,public.patient_details,public.shift", "message.key.columns": "public.ambulance:id;public.ambulance_checklist_item_update:id;public.ambulance_checklist_session:id;public.ambulance_order_trip_mapping:id;public.ambulance_staff_evaluation:id;public.ambulance_trip:id;public.ambulance_trip_event:id;public.ambulance_trip_staff:id;public.consumable:id;public.consumable_batch:id;public.depot:id;public.medical_remark:id;public.medical_staff:id;public.patient_details:id;public.shift:id", "name": "postgres.emergency_services.public.table_group_1.v1", "snapshot.mode": "exported", "database.server.name": "postgres.emergency_services", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "emergency_services", "slot.drop.on.stop": "false", "slot.name": "postgres_emergency_services_public_table_group_1_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.emergency_services.public.table_group_1.v1"}