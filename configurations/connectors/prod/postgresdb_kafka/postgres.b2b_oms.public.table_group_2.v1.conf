{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.consignment,public.consignment_container_mapping,public.consignment_container_status_log,public.consignment_invoice_mapping,public.consignment_item,public.consignment_status_log,public.consignment_transaction_entity", "message.key.columns": "public.consignment:id;public.consignment_container_mapping:id;public.consignment_container_status_log:id;public.consignment_invoice_mapping:id;public.consignment_item:id;public.consignment_status_log:id;public.consignment_transaction_entity:id", "name": "postgres.b2b_oms.public.table_group_2.v1", "snapshot.mode": "exported", "database.server.name": "postgres.b2b_oms", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "b2b_oms", "slot.drop.on.stop": "false", "slot.name": "postgres_b2b_oms_public_table_group_2_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.b2b_oms.public.table_group_2.v1"}