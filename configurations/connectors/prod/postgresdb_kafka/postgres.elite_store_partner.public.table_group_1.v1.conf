{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.electricity_bill,public.electricity_bill_final_status,public.electricity_bill_status,public.merchant_lead,public.store", "message.key.columns": "public.electricity_bill:id;public.electricity_bill_final_status:id;public.electricity_bill_status:id;public.merchant_lead:id;public.store:id", "name": "postgres.elite_store_partner.public.table_group_1.v1", "snapshot.mode": "exported", "database.server.name": "postgres.elite_store_partner", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "elite_store_partner", "slot.drop.on.stop": "false", "slot.name": "postgres_elite_store_partner_public_table_group_1_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.elite_store_partner.public.table_group_1.v1"}