{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.code_label_mapping,public.code_product_mapping,public.old_code_label_mapping,public.tenant", "message.key.columns": "public.code_label_mapping:code;public.code_product_mapping:code;public.old_code_label_mapping:id;public.tenant:id", "name": "postgres.asset_tracking.public.table_group_1.v1", "snapshot.mode": "exported", "database.server.name": "postgres.asset_tracking", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "asset_tracking", "slot.drop.on.stop": "false", "slot.name": "postgres_asset_tracking_public_table_group_1_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.asset_tracking.public.table_group_1.v1"}