{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.apob_facility,public.campaigns,public.cart,public.cart_order_mapping,public.cart_transaction_log,public.faq,public.item_outlet_inventory,public.rate_card_item,public.recall_request,public.recall_request_batch,public.recall_request_batch_item,public.recall_request_batch_log,public.recall_request_batch_pro_detail,public.sales_summary,public.scheduled_jobs,public.seller,public.seller_advertiser_mapping,public.seller_apob,public.seller_brand_detail,public.seller_document_info,public.seller_order_level_pos_sales_info,public.seller_order_level_sales_info,public.seller_payout_commission,public.seller_product_mappings,public.seller_product_request_mappings,public.seller_t_and_c,public.task,public.user_seller_mapping,public.workflow", "message.key.columns": "public.apob_facility:id;public.campaigns:id;public.cart:id;public.cart_order_mapping:id;public.cart_transaction_log:id;public.faq:id;public.item_outlet_inventory:id;public.rate_card_item:id;public.recall_request:id;public.recall_request_batch:id;public.recall_request_batch_item:id;public.recall_request_batch_log:id;public.recall_request_batch_pro_detail:id;public.sales_summary:id;public.scheduled_jobs:id;public.seller:id;public.seller_advertiser_mapping:id;public.seller_apob:id;public.seller_brand_detail:id;public.seller_document_info:id;public.seller_order_level_pos_sales_info:id;public.seller_order_level_sales_info:id;public.seller_payout_commission:id;public.seller_product_mappings:id;public.seller_product_request_mappings:id;public.seller_t_and_c:id;public.task:id;public.user_seller_mapping:id;public.workflow:id", "name": "postgres.seller.public.table_group_1.v1", "snapshot.mode": "exported", "database.server.name": "postgres.seller", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "seller", "slot.drop.on.stop": "false", "slot.name": "postgres_seller_public_table_group_1_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20", "column.exclude.list": "public.task.meta,public.workflow.meta"}, "name": "postgres.seller.public.table_group_1.v1"}