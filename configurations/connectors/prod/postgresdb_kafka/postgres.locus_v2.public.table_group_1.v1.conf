{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.document_classifications,public.document_form_mappings,public.document_records,public.form,public.form_final_status,public.form_final_status_lookup,public.form_team_status_mapping,public.network_team,public.project,public.project_final_status_lookup,public.status_type,public.sub_forms,public.team_final_status,public.user_data,public.user_ops_form_mapping,public.user_re_project_mapping,public.user_role_mapping,public.user_roles,public.user_zone_mapping,public.user_zones", "message.key.columns": "public.document_classifications:id;public.document_form_mappings:id;public.document_records:id;public.form:id;public.form_final_status:id;public.form_final_status_lookup:form_id,team_id,status_id;public.form_team_status_mapping:id;public.network_team:id;public.project:id;public.project_final_status_lookup:form_id;public.status_type:id;public.sub_forms:id;public.team_final_status:id;public.user_data:id;public.user_ops_form_mapping:id;public.user_re_project_mapping:id;public.user_role_mapping:user_id,role_id;public.user_roles:id;public.user_zone_mapping:user_id,zone_id;public.user_zones:id", "name": "postgres.locus_v2.public.table_group_1.v1", "snapshot.mode": "exported", "database.server.name": "postgres.locus_v2", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "locus_v2", "slot.drop.on.stop": "false", "slot.name": "postgres_locus_v2_public_table_group_1_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.locus_v2.public.table_group_1.v1"}