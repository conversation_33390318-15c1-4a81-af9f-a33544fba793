{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.ba_awareness_and_search_metrics,public.ba_brand_config,public.ba_brand_keyword_metrics,public.ba_category_insights_metrics,public.ba_market_basket_insights,public.ba_sales_metrics,public.ba_search_insights_metrics,public.ba_user_insight_metric", "message.key.columns": "public.ba_awareness_and_search_metrics:category_id,brand_id,city_name,metric;public.ba_brand_config:category_id,brand_id,config_type;public.ba_brand_keyword_metrics:brand_id,keyword,metric,city_name;public.ba_category_insights_metrics:category_id,metric,city_name,metric_type;public.ba_market_basket_insights:affinity_category_id,category_id,metric,city_name,brand_id;public.ba_sales_metrics:category_id,metric,city_name,brand_id;public.ba_search_insights_metrics:most_bought_product_id,keyword,category_id;public.ba_user_insight_metric:brand_id,category_id,metric,city_name", "name": "postgres.adserver_analytics.public.table_group_1.v1", "snapshot.mode": "exported", "database.server.name": "postgres.adserver_analytics", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "adserver_analytics", "slot.drop.on.stop": "false", "slot.name": "postgres_adserver_analytics_public_table_group_1_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.adserver_analytics.public.table_group_1.v1"}