{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.fleet_management_cost_item_attribution,public.fleet_management_cost_item_cost,public.fleet_management_cost_item_cost_log,public.fleet_management_cost_item_request_mapping,public.fleet_management_cost_request_log,public.fleet_management_trip_cost_item,public.fleet_management_trip_cost_request,public.fleet_management_vehicle_type", "message.key.columns": "public.fleet_management_cost_item_attribution:id;public.fleet_management_cost_item_cost:id;public.fleet_management_cost_item_cost_log:id;public.fleet_management_cost_item_request_mapping:id;public.fleet_management_cost_request_log:id;public.fleet_management_trip_cost_item:id;public.fleet_management_trip_cost_request:id;public.fleet_management_vehicle_type:id", "name": "postgres.fleet_management.public.table_group_2.v1", "snapshot.mode": "exported", "database.server.name": "postgres.fleet_management", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "fleet_management", "slot.drop.on.stop": "false", "slot.name": "postgres_fleet_management_public_table_group_2_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.fleet_management.public.table_group_2.v1"}