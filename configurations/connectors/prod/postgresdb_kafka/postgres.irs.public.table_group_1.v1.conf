{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "public.ewaybill,public.ewaybill_logs,public.irn,public.irn_logs,public.legal_entity,public.legal_identifier,public.vendor_functionality_whitelisting", "message.key.columns": "public.ewaybill:id;public.ewaybill_logs:id;public.irn:id;public.irn_logs:id;public.legal_entity:id;public.legal_identifier:id;public.vendor_functionality_whitelisting:id", "name": "postgres.irs.public.table_group_1.v1", "snapshot.mode": "exported", "database.server.name": "postgres.irs", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "irs", "slot.drop.on.stop": "false", "slot.name": "postgres_irs_public_table_group_1_v1", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.irs.public.table_group_1.v1"}