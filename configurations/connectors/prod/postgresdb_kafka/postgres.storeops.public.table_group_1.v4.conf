{"config": {"heartbeat.interval.ms": "60000", "heartbeat.action.query": "INSERT INTO public.debezium_connector_heartbeat (id, ts) VALUES (1, NOW()) ON CONFLICT(id) DO UPDATE SET ts=EXCLUDED.ts;", "table.include.list": "public.activity,public.asset_tracking,public.asset_tracking_log,public.business_eventdb,public.consignment_metrics,public.dbasync_task,public.discrepancy_logs,public.er,public.er_container,public.er_container_item,public.erline,public.inventory_update_log,public.inward_stock_attribute,public.item_activity,public.item_details,public.item_exceptions,public.item_site_tag_mapping,public.location,public.location_inventory,public.order_complaint,public.packaging_suggestion_details,public.poindent,public.poline,public.product_exception,public.product_info,public.site,public.user_details", "message.key.columns": "public.activity:id;public.asset_tracking:id;public.asset_tracking_log:id;public.business_eventdb:id;public.consignment_metrics:id;public.dbasync_task:id;public.discrepancy_logs:id;public.er:id;public.er_container:id;public.er_container_item:id;public.erline:id;public.inventory_update_log:id;public.inward_stock_attribute:id;public.item_activity:id;public.item_details:id;public.item_exceptions:id;public.item_site_tag_mapping:id;public.location:id;public.location_inventory:id;public.order_complaint:id;public.packaging_suggestion_details:id;public.poindent:id;public.poline:id;public.product_exception:id;public.product_info:id;public.site:id;public.user_details:id", "name": "postgres.storeops.public.table_group_1.v4", "snapshot.mode": "never", "database.server.name": "postgres.storeops", "connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "storeops", "slot.drop.on.stop": "false", "slot.name": "postgres_storeops_public_table_group_1_v4", "plugin.name": "pgoutput", "provide.transaction.metadata": "true", "producer.override.batch.size": "327680", "slot.max.retries": "20"}, "name": "postgres.storeops.public.table_group_1.v4"}