{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "rpc.ams_city_cluster_mapping,rpc.ams_cluster,rpc.attribute_outlet_entity_vendor,rpc.dated_tot_margin,rpc.facility_adjacency,rpc.facility_adjacency_log,rpc.facility_polygon,rpc.facility_polygon_log,rpc.item_tag_type,rpc.item_tag_type_value_mapping,rpc.new_store_assortment_request,rpc.new_store_assortment_request_log,rpc.off_invoice_rule,rpc.off_invoice_rule_claim,rpc.off_invoice_rule_instance,rpc.off_invoice_rule_target,rpc.off_invoice_rule_update_request,rpc.off_invoice_target_claim_entity,rpc.product_product_log,rpc.return_policy,rpc.return_policy_bucket_attribute,rpc.substitutable_group,rpc.substitutable_item_group,rpc.supply_event,rpc.supply_event_info,rpc.temporary_stock_details,rpc.temporary_stock_details_log,rpc.transfer_case_size,rpc.transfer_case_size_log,rpc.transfer_tag_rules,rpc.transfer_tag_rules_log,rpc.warehouse_transition", "message.key.columns": "rpc.ams_city_cluster_mapping:id;rpc.ams_cluster:cluster_id;rpc.attribute_outlet_entity_vendor:id;rpc.dated_tot_margin:id;rpc.facility_adjacency:id;rpc.facility_adjacency_log:id;rpc.facility_polygon:id;rpc.facility_polygon_log:id;rpc.item_tag_type:id;rpc.item_tag_type_value_mapping:id;rpc.new_store_assortment_request:id;rpc.new_store_assortment_request_log:id;rpc.off_invoice_rule:id;rpc.off_invoice_rule_claim:id;rpc.off_invoice_rule_instance:id;rpc.off_invoice_rule_target:id;rpc.off_invoice_rule_update_request:id;rpc.off_invoice_target_claim_entity:id;rpc.product_product_log:id;rpc.return_policy:id;rpc.return_policy_bucket_attribute:id;rpc.substitutable_group:id;rpc.substitutable_item_group:id;rpc.supply_event:id;rpc.supply_event_info:id;rpc.temporary_stock_details:id;rpc.temporary_stock_details_log:id;rpc.transfer_case_size:id;rpc.transfer_case_size_log:id;rpc.transfer_tag_rules:id;rpc.transfer_tag_rules_log:id;rpc.warehouse_transition:id", "name": "mysql.rpc.rpc.table_group_2.v3", "snapshot.mode": "schema_only", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.rpc", "database.history.kafka.topic": "db_history.mysql.rpc.table_group_2", "database.include.list": "rpc", "include.schema.changes": "true", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true", "tombstones.on.delete": "false", "converters": "boolean", "boolean.type": "io.debezium.connector.mysql.converters.TinyIntOneToBooleanConverter", "boolean.selector": "rpc.supply_event.active,rpc.supply_event_info.active,rpc.supply_event_info.exclude_sales,rpc.return_policy_bucket_attribute.active,rpc.return_policy.active,rpc.ams_city_cluster_mapping.active,rpc.off_invoice_rule.active,rpc.off_invoice_rule_instance.active,rpc.off_invoice_rule_update_request.active,rpc.transfer_tag_rules.active,rpc.substitutable_item_group.active,rpc.substitutable_group.ars_active,rpc.substitutable_group.active,rpc.off_invoice_rule_target.active,rpc.off_invoice_rule_claim.active,rpc.off_invoice_target_claim_entity.active,rpc.item_tag_type.active,rpc.new_store_assortment_request.active,rpc.transfer_case_size.active,rpc.transfer_case_size_log.active,rpc.warehouse_transition.active,rpc.dated_tot_margin.is_darkstore,rpc.dated_tot_margin.is_approved,rpc.dated_tot_margin.active,rpc.facility_adjacency_log.active,rpc.facility_polygon_log.active,rpc.facility_adjacency.active,rpc.facility_polygon.active,rpc.item_tag_type_value_mapping.active"}, "name": "mysql.rpc.rpc.table_group_2.v3"}