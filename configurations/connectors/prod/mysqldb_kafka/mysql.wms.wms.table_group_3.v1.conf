{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "wms.app_layout,wms.inbound_job_request,wms.internal_movement_container,wms.internal_movement_container_item,wms.unloading_task_po_invoice_mapping,wms.vehicle_registry,wms.vehicle_registry_log", "message.key.columns": "wms.app_layout:id;wms.inbound_job_request:id;wms.internal_movement_container:id;wms.internal_movement_container_item:id;wms.unloading_task_po_invoice_mapping:id;wms.vehicle_registry:id;wms.vehicle_registry_log:id", "name": "mysql.wms.wms.table_group_3.v1", "snapshot.mode": "initial", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.wms", "database.history.kafka.topic": "db_history.mysql.wms.table_group_3", "database.include.list": "wms", "include.schema.changes": "true", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true", "tombstones.on.delete": "false"}, "name": "mysql.wms.wms.table_group_3.v1"}