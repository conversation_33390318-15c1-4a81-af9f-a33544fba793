{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "bulk_upload.upload_job", "message.key.columns": "bulk_upload.upload_job:id", "name": "mysql.bulk_upload.bulk_upload.table_group_1.v2", "snapshot.mode": "initial", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.bulk_upload", "database.history.kafka.topic": "db_history.mysql.bulk_upload.table_group_1", "database.include.list": "bulk_upload", "include.schema.changes": "true", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true", "tombstones.on.delete": "false"}, "name": "mysql.bulk_upload.bulk_upload.table_group_1.v2"}