{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "warehouse_location.warehouse_storage_location_log", "message.key.columns": "warehouse_location.warehouse_storage_location_log:id", "name": "mysql.warehouse_location.warehouse_location.snapshot20250723.v1", "snapshot.mode": "initial_only", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.warehouse_location", "database.history.kafka.topic": "db_history.mysql.warehouse_location.table_group_3", "database.include.list": "warehouse_location", "include.schema.changes": "true", "database.initial.statements": "SET SESSION max_execution_time=0", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true"}, "name": "mysql.warehouse_location.warehouse_location.snapshot20250723.v1"}