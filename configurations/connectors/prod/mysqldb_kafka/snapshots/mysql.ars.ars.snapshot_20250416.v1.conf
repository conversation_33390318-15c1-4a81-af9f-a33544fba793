{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "ars.outlet_group_cpd", "message.key.columns": "ars.outlet_group_cpd:id", "name": "mysql.ars.ars.snapshot_20250416.v1", "snapshot.mode": "initial_only", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.ars", "database.history.kafka.topic": "db_history.mysql.ars.table_group_6", "database.include.list": "ars", "include.schema.changes": "true", "database.initial.statements": "SET SESSION max_execution_time=0", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true"}, "name": "mysql.ars.ars.snapshot_20250416.v1"}