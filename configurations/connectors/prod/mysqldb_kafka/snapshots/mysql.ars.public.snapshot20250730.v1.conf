{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "ars.item_constraint_details", "message.key.columns": "ars.item_constraint_details:id", "name": "mysql.ars.public.snapshot20250730.v1", "snapshot.mode": "initial_only", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.ars", "database.history.kafka.topic": "db_history.mysql.ars.table_group_5", "database.include.list": "ars", "include.schema.changes": "true", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true", "tombstones.on.delete": "false", "converters": "boolean", "boolean.type": "io.debezium.connector.mysql.converters.TinyIntOneToBooleanConverter", "boolean.selector": "ars.item_constraint_details.is_new_store,ars.item_constraint_details.is_live"}, "name": "mysql.ars.public.snapshot20250730.v1"}