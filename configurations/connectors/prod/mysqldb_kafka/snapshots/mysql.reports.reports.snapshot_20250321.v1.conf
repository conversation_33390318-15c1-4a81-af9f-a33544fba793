{
  "config": {
    "heartbeat.interval.ms": "60000",
    "table.include.list": "reports.inventory_snapshot_run",
    "message.key.columns": "reports.inventory_snapshot_run:id",
    "name": "mysql.reports.reports.snapshot_20250321.v1",
    "snapshot.mode": "initial_only",
    "snapshot.locking.mode": "none",
    "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092",
    "connector.class": "io.debezium.connector.mysql.MySqlConnector",
    "database.server.name": "mysql.reports",
    "database.history.kafka.topic": "db_history.mysql.reports.table_group_1",
    "database.include.list": "reports",
    "include.schema.changes": "true",
    "database.initial.statements": "SET SESSION max_execution_time=0",
    "producer.override.batch.size": "327680",
    "database.history.skip.unparseable.ddl": "true",
  },
  "name": "mysql.reports.reports.snapshot_20250321.v1"
}
