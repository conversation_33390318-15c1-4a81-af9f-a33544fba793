{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "ars.b2b_transfer_rules,ars.b2b_transfer_rule_attributes,ars.b2b_item_outlet_mapping,ars.b2b_item_outlet_mapping_log", "message.key.columns": "ars.b2b_transfer_rules:id;ars.b2b_transfer_rule_attributes:id;ars.b2b_item_outlet_mapping:id;ars.b2b_item_outlet_mapping_log:id", "name": "mysql.ars.ars.snapshot20250819.v1", "snapshot.mode": "initial_only", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.ars", "database.history.kafka.topic": "db_history.mysql.ars.table_group_6", "database.include.list": "ars", "include.schema.changes": "true", "database.initial.statements": "SET SESSION max_execution_time=0", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true", "converters": "boolean", "boolean.type": "io.debezium.connector.mysql.converters.TinyIntOneToBooleanConverter", "boolean.selector": "ars.b2b_transfer_rules.active,ars.b2b_item_outlet_mapping.active,ars.b2b_item_outlet_mapping_log.active"}, "name": "mysql.ars.ars.snapshot20250819.v1"}