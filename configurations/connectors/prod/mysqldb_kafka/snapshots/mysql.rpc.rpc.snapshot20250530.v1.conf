{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "rpc.warehouse_transition", "message.key.columns": "rpc.warehouse_transition:id", "name": "mysql.rpc.rpc.snapshot20250530.v1", "snapshot.mode": "initial_only", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.rpc", "database.history.kafka.topic": "db_history.mysql.rpc.table_group_2", "database.include.list": "rpc", "include.schema.changes": "true", "database.initial.statements": "SET SESSION max_execution_time=0", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true", "converters": "boolean", "boolean.type": "io.debezium.connector.mysql.converters.TinyIntOneToBooleanConverter", "boolean.selector": "rpc.warehouse_transition.active"}, "name": "mysql.rpc.rpc.snapshot20250530.v1"}