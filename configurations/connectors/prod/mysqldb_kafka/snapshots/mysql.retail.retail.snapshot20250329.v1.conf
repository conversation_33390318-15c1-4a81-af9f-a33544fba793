{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "retail.warehouse_sub_department", "message.key.columns": "retail.warehouse_sub_department:id", "name": "mysql.retail.retail.snapshot20250329.v1", "snapshot.mode": "initial_only", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.retail", "database.history.kafka.topic": "db_history.mysql.retail.table_group_1", "database.include.list": "retail", "include.schema.changes": "true", "database.initial.statements": "SET SESSION max_execution_time=0", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true", "converters": "boolean", "boolean.type": "io.debezium.connector.mysql.converters.TinyIntOneToBooleanConverter", "boolean.selector": "retail.warehouse_sub_department.enabled"}, "name": "mysql.retail.retail.snapshot20250329.v1"}