{
  "config": {
    "heartbeat.interval.ms": "60000",
    "table.include.list": "promo_service.user",
    "message.key.columns": "promo_service.user:id",
    "name": "mysql.promo_service.promo_service.snapshot_20250416.v1",
    "snapshot.mode": "initial_only",
    "snapshot.locking.mode": "none",
    "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092",
    "connector.class": "io.debezium.connector.mysql.MySqlConnector",
    "database.server.name": "mysql.promo_service",
    "database.history.kafka.topic": "db_history.mysql.promo_service.table_group_1",
    "database.include.list": "promo_service",
    "include.schema.changes": "true",
    "database.initial.statements": "SET SESSION max_execution_time=0",
    "producer.override.batch.size": "327680",
    "database.history.skip.unparseable.ddl": "true",
    "converters": "boolean",
    "boolean.type": "io.debezium.connector.mysql.converters.TinyIntOneToBooleanConverter",
    "boolean.selector": "promo_service.user.is_active",
  },
  "name": "mysql.promo_service.promo_service.snapshot_20250416.v1"
}
