{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "ars.ars_job_run,ars.job_run_location,ars.job_run_snapshot", "message.key.columns": "ars.ars_job_run:run_id;ars.job_run_location:id;ars.job_run_snapshot:id", "name": "mysql.ars.ars.snapshot_20250704.v1", "snapshot.mode": "initial_only", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.ars", "database.history.kafka.topic": "db_history.mysql.ars.table_group_2", "database.include.list": "ars", "include.schema.changes": "true", "database.initial.statements": "SET SESSION max_execution_time=0", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true"}, "name": "mysql.ars.ars.snapshot_20250704.v1"}