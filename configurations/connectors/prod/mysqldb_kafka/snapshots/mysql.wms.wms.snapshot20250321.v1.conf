{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "wms.bad_stock_container,wms.bad_stock_item", "message.key.columns": "wms.bad_stock_container:id;wms.bad_stock_item:id", "name": "mysql.wms.wms.snapshot20250321.v1", "snapshot.mode": "initial_only", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.wms", "database.history.kafka.topic": "db_history.mysql.wms.table_group_2", "database.include.list": "wms", "include.schema.changes": "true", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true", "converters": "boolean", "boolean.type": "io.debezium.connector.mysql.converters.TinyIntOneToBooleanConverter", "boolean.selector": "wms.bad_stock_item.is_rtv_eligible,wms.bad_stock_container.is_rtv_eligible"}, "name": "mysql.wms.wms.snapshot20250321.v1"}