{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "ars.bulk_process_ordering_override_tracker,ars.ordering_override_log,ars.ordering_overrides", "message.key.columns": "ars.bulk_process_ordering_override_tracker:id;ars.ordering_override_log:id;ars.ordering_overrides:id", "name": "mysql.ars.ars.snapshot_20250702.v1", "snapshot.mode": "initial_only", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.ars", "database.history.kafka.topic": "db_history.mysql.ars.table_group_6", "database.include.list": "ars", "include.schema.changes": "true", "database.initial.statements": "SET SESSION max_execution_time=0", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true", "tombstones.on.delete": "false", "converters": "boolean", "boolean.type": "io.debezium.connector.mysql.converters.TinyIntOneToBooleanConverter", "boolean.selector": "ars.ordering_override_log.active,ars.ordering_overrides.active,ars.bulk_process_ordering_override_tracker.active"}, "name": "mysql.ars.ars.snapshot_20250702.v1"}