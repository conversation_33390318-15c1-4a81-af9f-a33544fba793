{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "po.invoice_adjustment_details,po.invoice_dn_details,po.invoice_grn_details", "message.key.columns": "po.invoice_adjustment_details:id;po.invoice_dn_details:id;po.invoice_grn_details:id", "name": "mysql.po.po.snapshot20250709.v1", "snapshot.mode": "initial_only", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.po", "database.history.kafka.topic": "db_history.mysql.po.table_group_2", "database.include.list": "po", "include.schema.changes": "true", "database.initial.statements": "SET SESSION max_execution_time=0", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true", "converters": "boolean", "boolean.type": "io.debezium.connector.mysql.converters.TinyIntOneToBooleanConverter", "boolean.selector": "po.invoice_adjustment_details.active,po.invoice_dn_details.active,po.invoice_grn_details.active"}, "name": "mysql.po.po.snapshot20250709.v1"}