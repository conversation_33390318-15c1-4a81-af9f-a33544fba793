{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "wms.app_layout", "message.key.columns": "wms.app_layout:id", "name": "mysql.wms.wms.snapshot2025072.v1", "snapshot.mode": "initial_only", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.wms", "database.history.kafka.topic": "db_history.mysql.wms.table_group_3", "database.include.list": "wms", "include.schema.changes": "true", "database.initial.statements": "SET SESSION max_execution_time=0", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true"}, "name": "mysql.wms.wms.snapshot2025072.v1"}