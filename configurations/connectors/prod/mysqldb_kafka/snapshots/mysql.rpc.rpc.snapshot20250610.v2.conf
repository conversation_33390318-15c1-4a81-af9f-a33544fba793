{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "rpc.attribute_outlet_entity_vendor", "message.key.columns": "rpc.attribute_outlet_entity_vendor:id", "name": "mysql.rpc.rpc.snapshot20250610.v2", "snapshot.mode": "initial_only", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.rpc", "database.history.kafka.topic": "db_history.mysql.rpc.table_group_2", "database.include.list": "rpc", "include.schema.changes": "true", "database.initial.statements": "SET SESSION max_execution_time=0", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true"}, "name": "mysql.rpc.rpc.snapshot20250610.v2"}