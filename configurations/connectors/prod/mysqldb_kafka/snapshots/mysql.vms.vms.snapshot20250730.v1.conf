{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "vms.facility_vendor_address", "message.key.columns": "vms.facility_vendor_address:id", "name": "mysql.vms.vms.snapshot20250730.v1", "snapshot.mode": "initial_only", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.vms", "database.history.kafka.topic": "db_history.mysql.vms.table_group_1", "database.include.list": "vms", "include.schema.changes": "true", "database.initial.statements": "SET SESSION max_execution_time=0", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true", "converters": "boolean", "boolean.type": "io.debezium.connector.mysql.converters.TinyIntOneToBooleanConverter", "boolean.selector": "vms.facility_vendor_address.active"}, "name": "mysql.vms.vms.snapshot20250730.v1"}