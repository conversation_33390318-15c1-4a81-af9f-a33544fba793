{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "po.proof_of_delivery_items,po.proof_of_delivery", "message.key.columns": "po.proof_of_delivery_items:id;po.proof_of_delivery_items,po.proof_of_delivery:id", "name": "mysql.po.po.snapshot20250721.v1", "snapshot.mode": "initial_only", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.po", "database.history.kafka.topic": "db_history.mysql.po.table_group_2", "database.include.list": "po", "include.schema.changes": "true", "database.initial.statements": "SET SESSION max_execution_time=0", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true", "converters": "boolean", "boolean.type": "io.debezium.connector.mysql.converters.TinyIntOneToBooleanConverter", "boolean.selector": "po.proof_of_delivery.is_segregated_on_po_level"}, "name": "mysql.po.po.snapshot20250721.v1"}