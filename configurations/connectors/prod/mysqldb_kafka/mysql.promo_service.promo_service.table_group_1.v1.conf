{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "promo_service.campaign_conditions,promo_service.campaign_history,promo_service.campaigns,promo_service.conditions,promo_service.expressions,promo_service.offer_budget,promo_service.offers,promo_service.promo_codes,promo_service.promos,promo_service.salts,promo_service.user", "message.key.columns": "promo_service.campaign_conditions:condition_id;promo_service.campaign_history:id;promo_service.campaigns:campaign_id;promo_service.conditions:condition_id;promo_service.expressions:expression_id;promo_service.offer_budget:budget_id;promo_service.offers:offer_id;promo_service.promo_codes:code_id;promo_service.promos:promo_id;promo_service.salts:salt_id;promo_service.user:user_id", "name": "mysql.promo_service.promo_service.table_group_1.v1", "snapshot.mode": "initial", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.promo_service", "database.history.kafka.topic": "db_history.mysql.promo_service.table_group_1", "database.include.list": "promo_service", "include.schema.changes": "true", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true", "converters": "boolean", "boolean.type": "io.debezium.connector.mysql.converters.TinyIntOneToBooleanConverter", "boolean.selector": "promo_service.promos.is_active,promo_service.promos.auto_apply,promo_service.promos.is_silent,promo_service.promos.is_promoted,promo_service.promo_codes.is_active,promo_service.conditions.is_active,promo_service.offers.is_active,promo_service.campaigns.is_active,promo_service.campaigns.is_logged_out_user_campaign,promo_service.offer_budget.is_active,promo_service.user.is_active,promo_service.campaign_conditions.is_active", "tombstones.on.delete": "false"}, "name": "mysql.promo_service.promo_service.table_group_1.v1"}