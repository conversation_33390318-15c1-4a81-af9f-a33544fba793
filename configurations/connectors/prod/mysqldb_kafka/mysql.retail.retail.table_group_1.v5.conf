{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "retail.auth_user,retail.auth_user_log_v2,retail.console_business_type,retail.console_company_type,retail.console_device,retail.console_excess_transfer_merchant,retail.console_location,retail.console_merchant,retail.console_outlet,retail.console_outlet_cms_store,retail.console_outlet_default_entity_vendor,retail.console_outlet_logistic_mapping,retail.console_outlet_merchant,retail.console_state,retail.outlet_entity_vendor_mapping,retail.user_entity_management,retail.user_management_group,retail.user_management_user_group,retail.warehouse_facility,retail.warehouse_outlet_mapping,retail.warehouse_sub_department,retail.warehouse_user,retail.warehouse_user_access_group,retail.warehouse_user_auth_token,retail.warehouse_user_designation,retail.warehouse_user_log,retail.warehouse_user_role", "message.key.columns": "retail.auth_user:id;retail.auth_user_log_v2:id;retail.console_business_type:id;retail.console_company_type:id;retail.console_device:id;retail.console_excess_transfer_merchant:id;retail.console_location:id;retail.console_merchant:id;retail.console_outlet:id;retail.console_outlet_cms_store:id;retail.console_outlet_default_entity_vendor:id;retail.console_outlet_logistic_mapping:id;retail.console_outlet_merchant:id;retail.console_state:id;retail.outlet_entity_vendor_mapping:id;retail.user_entity_management:id;retail.user_management_group:id;retail.user_management_user_group:id;retail.warehouse_facility:id;retail.warehouse_outlet_mapping:id;retail.warehouse_sub_department:id;retail.warehouse_user:id;retail.warehouse_user_access_group:id;retail.warehouse_user_auth_token:id;retail.warehouse_user_designation:id;retail.warehouse_user_log:local_id;retail.warehouse_user_role:id", "name": "mysql.retail.retail.table_group_1.v5", "snapshot.mode": "schema_only", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.retail", "database.history.kafka.topic": "db_history.mysql.retail.table_group_1", "database.include.list": "retail", "include.schema.changes": "true", "converters": "boolean", "boolean.type": "io.debezium.connector.mysql.converters.TinyIntOneToBooleanConverter", "boolean.selector": "retail.warehouse_user_designation.enabled,retail.console_outlet_merchant.active,retail.console_excess_transfer_merchant.active,retail.warehouse_user.is_mobile_number_updated,retail.console_outlet.on_new_store_ops,retail.console_outlet_logistic_mapping.active,retail.console_outlet_logistic_mapping.primary_node,retail.user_entity_management.active,retail.user_management_user_group.active,retail.warehouse_sub_department.enabled", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true", "tombstones.on.delete": "false"}, "name": "mysql.retail.retail.table_group_1.v5"}