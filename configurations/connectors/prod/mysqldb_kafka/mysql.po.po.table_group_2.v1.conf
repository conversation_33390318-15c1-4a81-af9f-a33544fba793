{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "po.asn_item_level_validations,po.edi_asn_response,po.edi_integration_partner_invoice_item_serial_mapping,po.invoice_adjustment_details,po.invoice_dn_details,po.invoice_grn_details,po.proof_of_delivery,po.proof_of_delivery_items", "message.key.columns": "po.asn_item_level_validations:id;po.edi_asn_response:id;po.edi_integration_partner_invoice_item_serial_mapping:id;po.invoice_adjustment_details:id;po.invoice_dn_details:id;po.invoice_grn_details:id;po.proof_of_delivery:id;po.proof_of_delivery_items:id", "name": "mysql.po.po.table_group_2.v1", "snapshot.mode": "initial", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.po", "database.history.kafka.topic": "db_history.mysql.po.table_group_2", "database.include.list": "po", "include.schema.changes": "true", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true", "tombstones.on.delete": "false", "converters": "boolean", "boolean.type": "io.debezium.connector.mysql.converters.TinyIntOneToBooleanConverter", "boolean.selector": "po.edi_integration_partner_invoice_item_serial_mapping.active,po.asn_item_level_validations.is_latest,po.invoice_adjustment_details.active,po.invoice_dn_details.active,po.invoice_grn_details.active,po.proof_of_delivery.is_segregated_on_po_level"}, "name": "mysql.po.po.table_group_2.v1"}