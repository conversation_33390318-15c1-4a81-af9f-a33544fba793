{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "warehouse_location.batch_logic_log,warehouse_location.billing_entity_crate_mapping,warehouse_location.billing_entity_invoice_mapping,warehouse_location.case_variant_entity_mapping,warehouse_location.case_variant_entity_state_log,warehouse_location.item_zone_mapping,warehouse_location.item_zone_mapping_log,warehouse_location.label_product_mapping,warehouse_location.label_product_mapping_log,warehouse_location.leaderboard_metrics,warehouse_location.migration_task,warehouse_location.migration_task_activity,warehouse_location.migration_task_location_log,warehouse_location.migration_task_state_log,warehouse_location.pick_list_item_crate_mapping,warehouse_location.warehouse_aisle_rack_type,warehouse_location.warehouse_picking_zone,warehouse_location.warehouse_picklist_to_crate_mapping,warehouse_location.warehouse_picklist_to_irt_picklist_mapping,warehouse_location.warehouse_storage_location_log", "message.key.columns": "warehouse_location.batch_logic_log:log_id;warehouse_location.billing_entity_crate_mapping:id;warehouse_location.billing_entity_invoice_mapping:id;warehouse_location.case_variant_entity_mapping:case_id;warehouse_location.case_variant_entity_state_log:id;warehouse_location.item_zone_mapping:id;warehouse_location.item_zone_mapping_log:id;warehouse_location.label_product_mapping:id;warehouse_location.label_product_mapping_log:id;warehouse_location.leaderboard_metrics:id;warehouse_location.migration_task:id;warehouse_location.migration_task_activity:id;warehouse_location.migration_task_location_log:id;warehouse_location.migration_task_state_log:id;warehouse_location.pick_list_item_crate_mapping:id;warehouse_location.warehouse_aisle_rack_type:id;warehouse_location.warehouse_picking_zone:id;warehouse_location.warehouse_picklist_to_crate_mapping:id;warehouse_location.warehouse_picklist_to_irt_picklist_mapping:id;warehouse_location.warehouse_storage_location_log:id", "name": "mysql.warehouse_location.warehouse_location.table_group_3.v4", "snapshot.mode": "schema_only", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.warehouse_location", "database.history.kafka.topic": "db_history.mysql.warehouse_location.table_group_3", "database.include.list": "warehouse_location", "include.schema.changes": "true", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true", "tombstones.on.delete": "false", "converters": "boolean", "boolean.type": "io.debezium.connector.mysql.converters.TinyIntOneToBooleanConverter", "boolean.selector": "warehouse_location.item_zone_mapping.is_spr,warehouse_location.item_zone_mapping.active,warehouse_location.item_zone_mapping_log.active,warehouse_location.item_zone_mapping_log.is_spr,warehouse_location.picker_variable_incentive_log.processed,warehouse_location.reasons.active,warehouse_location.migration_task.is_case_movt_eligible,warehouse_location.item_suggested_location_mapping.active,warehouse_location.warehouse_picking_zone.enabled,warehouse_location.warehouse_aisle_rack_type.is_active"}, "name": "mysql.warehouse_location.warehouse_location.table_group_3.v4"}