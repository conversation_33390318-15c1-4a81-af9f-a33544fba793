{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "reports.inventory_snapshot_run,reports.reports_entity_bad_inventory_snapshot,reports.reports_entity_good_inventory_snapshot,reports.reports_entity_inventory_snapshot", "message.key.columns": "reports.inventory_snapshot_run:id;reports.reports_entity_bad_inventory_snapshot:id;reports.reports_entity_good_inventory_snapshot:id;reports.reports_entity_inventory_snapshot:id", "name": "mysql.reports.reports.table_group_1.v6", "snapshot.mode": "schema_only", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.reports", "database.history.kafka.topic": "db_history.mysql.reports.table_group_1", "database.include.list": "reports", "include.schema.changes": "true", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true", "tombstones.on.delete": "false", "converters": "boolean", "boolean.type": "io.debezium.connector.mysql.converters.TinyIntOneToBooleanConverter", "boolean.selector": "reports.reports_entity_bad_inventory_snapshot.active,reports.reports_entity_good_inventory_snapshot.active,reports.reports_entity_inventory_snapshot.active"}, "name": "mysql.reports.reports.table_group_1.v6"}