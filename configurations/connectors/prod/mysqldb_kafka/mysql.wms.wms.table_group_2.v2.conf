{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "wms.audit_activity,wms.audit_crate,wms.audit_crate_item,wms.audit_job,wms.audit_job_line_item,wms.audit_job_log,wms.audit_user,wms.audit_user_log,wms.bad_stock_container,wms.bad_stock_drop_zone,wms.bad_stock_inventory_log,wms.bad_stock_item,wms.billing_group,wms.billing_group_order_invoice_tracker,wms.container_migration_task,wms.container_migration_zone_map,wms.dispatch_consignment,wms.dispatch_container,wms.dispatch_trip,wms.dispatch_zone,wms.employee_activity,wms.employee_break,wms.employee_session,wms.entity_config_mappings,wms.entity_group,wms.external_order_activity_invoice_tracker,wms.handover_qc_activity,wms.handover_qc_activity_container,wms.handover_qc_activity_item,wms.inbound_financial_transaction,wms.item_audit,wms.migration_container,wms.ob_pkg_entity_container_log,wms.ob_pkg_entity_item,wms.ob_pkg_entity_item_actual,wms.ob_pkg_external_order_activity,wms.ob_pkg_item_ledger,wms.ob_pkg_outbound_container,wms.ob_pkg_outbound_container_log,wms.order,wms.order_cancellation,wms.order_cancellation_record,wms.order_item,wms.outbound_billing_item,wms.outbound_config_log,wms.outbound_container,wms.outbound_demand,wms.outbound_invoice,wms.outbound_item,wms.packaging_bucket_variant,wms.packaging_task_item,wms.packaging_tasks,wms.pick_list,wms.pick_list_container_mapping,wms.pick_list_item,wms.pick_list_item_container_mapping,wms.pick_list_item_location_variant_batch,wms.pick_list_item_log,wms.pick_list_item_order_mapping,wms.pick_list_log,wms.picker_zone_allocation_mapping,wms.picker_zone_allocation_mapping_log,wms.picker_zone_preferences,wms.picker_zone_preferences_log,wms.picking_slot_demand_details,wms.picking_slot_demand_details_log,wms.picking_slot_info,wms.picking_type_rate,wms.put_list,wms.put_list_item,wms.put_list_item_locations,wms.put_list_state_transition_log,wms.putlist_bucket,wms.putlist_bucket_event,wms.qc_merchant_score,wms.qc_outlet_config,wms.qc_picker_score,wms.segregation_task,wms.segregation_task_item,wms.segregation_task_item_pallet_mapping,wms.segregation_task_pallet_mapping,wms.station,wms.station_log,wms.unloading_attribution,wms.unloading_container,wms.unloading_group,wms.unloading_group_image,wms.unloading_proof_of_delivery,wms.unloading_task_expected_item,wms.user_attendance,wms.wave", "message.key.columns": "wms.audit_activity:id;wms.audit_crate:id;wms.audit_crate_item:id;wms.audit_job:id;wms.audit_job_line_item:id;wms.audit_job_log:id;wms.audit_user:id;wms.audit_user_log:id;wms.bad_stock_container:id;wms.bad_stock_drop_zone:id;wms.bad_stock_inventory_log:id;wms.bad_stock_item:id;wms.billing_group:id;wms.billing_group_order_invoice_tracker:id;wms.container_migration_task:id;wms.container_migration_zone_map:id;wms.dispatch_consignment:id;wms.dispatch_container:id;wms.dispatch_trip:id;wms.dispatch_zone:id;wms.employee_activity:id;wms.employee_break:id;wms.employee_session:id;wms.entity_config_mappings:id;wms.entity_group:id;wms.external_order_activity_invoice_tracker:id;wms.handover_qc_activity:id;wms.handover_qc_activity_container:id;wms.handover_qc_activity_item:id;wms.inbound_financial_transaction:id;wms.item_audit:id;wms.migration_container:id;wms.ob_pkg_entity_container_log:id;wms.ob_pkg_entity_item:id;wms.ob_pkg_entity_item_actual:id;wms.ob_pkg_external_order_activity:id;wms.ob_pkg_item_ledger:id;wms.ob_pkg_outbound_container:id;wms.ob_pkg_outbound_container_log:id;wms.order:id;wms.order_cancellation:id;wms.order_cancellation_record:id;wms.order_item:id;wms.outbound_billing_item:id;wms.outbound_config_log:id;wms.outbound_container:id;wms.outbound_demand:id;wms.outbound_invoice:id;wms.outbound_item:id;wms.packaging_bucket_variant:id;wms.packaging_task_item:id;wms.packaging_tasks:id;wms.pick_list:id;wms.pick_list_container_mapping:id;wms.pick_list_item:id;wms.pick_list_item_container_mapping:id;wms.pick_list_item_location_variant_batch:id;wms.pick_list_item_log:id;wms.pick_list_item_order_mapping:id;wms.pick_list_log:id;wms.picker_zone_allocation_mapping:id;wms.picker_zone_allocation_mapping_log:id;wms.picker_zone_preferences:id;wms.picker_zone_preferences_log:id;wms.picking_slot_demand_details:id;wms.picking_slot_demand_details_log:id;wms.picking_slot_info:id;wms.picking_type_rate:id;wms.put_list:id;wms.put_list_item:id;wms.put_list_item_locations:id;wms.put_list_state_transition_log:id;wms.putlist_bucket:id;wms.putlist_bucket_event:id;wms.qc_merchant_score:id;wms.qc_outlet_config:outlet_id;wms.qc_picker_score:id;wms.segregation_task:id;wms.segregation_task_item:id;wms.segregation_task_item_pallet_mapping:id;wms.segregation_task_pallet_mapping:id;wms.station:id;wms.station_log:id;wms.unloading_attribution:id;wms.unloading_container:id;wms.unloading_group:id;wms.unloading_group_image:id;wms.unloading_proof_of_delivery:id;wms.unloading_task_expected_item:id;wms.user_attendance:id;wms.wave:id", "name": "mysql.wms.wms.table_group_2.v2", "snapshot.mode": "initial", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.wms", "database.history.kafka.topic": "db_history.mysql.wms.table_group_2", "database.include.list": "wms", "include.schema.changes": "true", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true", "producer.override.max.request.size": "20971520", "converters": "boolean", "boolean.type": "io.debezium.connector.mysql.converters.TinyIntOneToBooleanConverter", "boolean.selector": "wms.bad_stock_drop_zone.active,wms.dispatch_zone.active,wms.entity_group.active,wms.put_list.pushed_to_app,wms.picker_zone_allocation_mapping.logged_in,wms.picking_slot_info.active,wms.picker_zone_allocation_mapping_log.logged_in,wms.packaging_task_item.processed,wms.dispatch_consignment.partial_status,wms.pick_list_container_mapping.active,wms.employee_activity.on_break,wms.billing_group.enqueued,wms.billing_group_order_invoice_tracker.enqueued,wms.station.enabled,wms.station_log.enabled,wms.ob_pkg_external_order_activity.enqueued,wms.bad_stock_item.is_rtv_eligible,wms.bad_stock_container.is_rtv_eligible,wms.user_attendance.is_active", "tombstones.on.delete": "false"}, "name": "mysql.wms.wms.table_group_2.v2"}