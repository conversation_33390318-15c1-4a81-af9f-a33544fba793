{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "vms.contact_address,vms.contact_electronic,vms.contact_model,vms.contact_phone,vms.contact_purpose,vms.contact_type,vms.contact_vendor,vms.delivery_type,vms.external_api_call_logs,vms.facility_vendor_address,vms.io_max_doi,vms.io_max_doi_log,vms.iov_fill_rate_buffer_doi,vms.item_facility_group_mapping,vms.item_facility_group_mapping_log,vms.load_group_attributes,vms.load_group_attributes_log,vms.po_cycle,vms.po_cycle_types,vms.vendor_approval,vms.vendor_config,vms.vendor_config_log,vms.vendor_contact_address,vms.vendor_item_physical_facility_attributes,vms.vendor_item_physical_facility_attributes_log,vms.vms_company,vms.vms_document_type,vms.vms_line_of_business,vms.vms_manufacturer_contact_details,vms.vms_ownership_types,vms.vms_vendor,vms.vms_vendor_alignment,vms.vms_vendor_alignment_log,vms.vms_vendor_alignment_state,vms.vms_vendor_city_documents,vms.vms_vendor_city_mapping,vms.vms_vendor_city_product_details,vms.vms_vendor_city_tax_info,vms.vms_vendor_facility_alignment,vms.vms_vendor_facility_alignment_log,vms.vms_vendor_group_mapping,vms.vms_vendor_merchant_mapping,vms.vms_vendor_new_pi_logic,vms.vms_vendor_new_pi_logic_log,vms.vms_vendor_pi_logic", "message.key.columns": "vms.contact_address:id;vms.contact_electronic:id;vms.contact_model:id;vms.contact_phone:id;vms.contact_purpose:id;vms.contact_type:id;vms.contact_vendor:id;vms.delivery_type:id;vms.external_api_call_logs:id;vms.facility_vendor_address:id;vms.io_max_doi:id;vms.io_max_doi_log:id;vms.iov_fill_rate_buffer_doi:id;vms.item_facility_group_mapping:id;vms.item_facility_group_mapping_log:id;vms.load_group_attributes:id;vms.load_group_attributes_log:id;vms.po_cycle:id;vms.po_cycle_types:id;vms.vendor_approval:id;vms.vendor_config:id;vms.vendor_config_log:id;vms.vendor_contact_address:id;vms.vendor_item_physical_facility_attributes:id;vms.vendor_item_physical_facility_attributes_log:id;vms.vms_company:id;vms.vms_document_type:id;vms.vms_line_of_business:id;vms.vms_manufacturer_contact_details:id;vms.vms_ownership_types:id;vms.vms_vendor:id;vms.vms_vendor_alignment:id;vms.vms_vendor_alignment_log:id;vms.vms_vendor_alignment_state:id;vms.vms_vendor_city_documents:id;vms.vms_vendor_city_mapping:id;vms.vms_vendor_city_product_details:id;vms.vms_vendor_city_tax_info:id;vms.vms_vendor_facility_alignment:id;vms.vms_vendor_facility_alignment_log:id;vms.vms_vendor_group_mapping:group_id;vms.vms_vendor_merchant_mapping:id;vms.vms_vendor_new_pi_logic:id;vms.vms_vendor_new_pi_logic_log:id;vms.vms_vendor_pi_logic:id", "name": "mysql.vms.vms.table_group_1.v4", "snapshot.mode": "schema_only", "snapshot.locking.mode": "none", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.server.name": "mysql.vms", "database.history.kafka.topic": "db_history.mysql.vms.table_group_1", "database.include.list": "vms", "include.schema.changes": "true", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true", "tombstones.on.delete": "false", "converters": "boolean", "boolean.type": "io.debezium.connector.mysql.converters.TinyIntOneToBooleanConverter", "boolean.selector": "vms.contact_type.active,vms.contact_type.is_unique,vms.vms_vendor_city_documents.active,vms.vms_document_type.active,vms.item_facility_group_mapping.active,vms.load_group_attributes.active,vms.vms_company.active,vms.po_cycle_types.active,vms.vms_vendor_new_pi_logic.active,vms.vms_vendor_group_mapping.active,vms.vms_vendor_new_pi_logic_log.active,vms.vms_vendor_new_pi_logic_log.multi_grn,vms.vms_vendor_new_pi_logic.multi_grn,vms.vendor_contact_address.active,vms.facility_vendor_address.active"}, "name": "mysql.vms.vms.table_group_1.v4"}