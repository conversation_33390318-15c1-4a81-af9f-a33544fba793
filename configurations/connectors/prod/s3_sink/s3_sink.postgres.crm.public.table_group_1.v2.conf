{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "postgres.crm.public.agent_handover_request,postgres.crm.public.crm_agent_profile,postgres.crm.public.crm_agent_resolution,postgres.crm.public.crm_call_logs_entry,postgres.crm.public.crm_chat_media,postgres.crm.public.crm_dashboard_permission,postgres.crm.public.crm_device_block_action_history,postgres.crm.public.crm_force_update_karma,postgres.crm.public.crm_helpdesk_node,postgres.crm.public.crm_helpdesk_rule,postgres.crm.public.crm_issue,postgres.crm.public.crm_issue_item,postgres.crm.public.crm_issue_type,postgres.crm.public.crm_order_tag,postgres.crm.public.crm_order_task,postgres.crm.public.crm_permission_update_event,postgres.crm.public.crm_refund,postgres.crm.public.crm_self_service_action,postgres.crm.public.crm_source,postgres.crm.public.crm_tag,postgres.crm.public.crm_user,postgres.crm.public.crm_user_permission,postgres.crm.public.delight_money_transaction,postgres.crm.public.user_abuse_details", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "name": "s3_sink.postgres.crm.public.table_group_1.v2"}, "name": "s3_sink.postgres.crm.public.table_group_1.v2"}