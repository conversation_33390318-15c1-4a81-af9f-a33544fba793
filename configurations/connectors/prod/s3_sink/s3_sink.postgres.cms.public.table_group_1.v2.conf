{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "postgres.cms.public.entity_rules,postgres.cms.public.gr_attribute,postgres.cms.public.gr_attribute_option_value,postgres.cms.public.gr_brand,postgres.cms.public.gr_brand_category_mapping,postgres.cms.public.gr_company,postgres.cms.public.gr_coordinate,postgres.cms.public.gr_group,postgres.cms.public.gr_group_attribute_mapping,postgres.cms.public.gr_locality,postgres.cms.public.gr_merchant,postgres.cms.public.gr_merchant_additional_info,postgres.cms.public.gr_merchant_category_mapping,postgres.cms.public.gr_merchant_chain,postgres.cms.public.gr_merchant_finance_info,postgres.cms.public.gr_tag,postgres.cms.public.gr_user,postgres.cms.public.gr_user_merchant,postgres.cms.public.gr_user_role_mapping,postgres.cms.public.gr_virtual_to_real_merchant_mapping", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "name": "s3_sink.postgres.cms.public.table_group_1.v2"}, "name": "s3_sink.postgres.cms.public.table_group_1.v2"}