{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "postgres.locus_v2.public.document_classifications,postgres.locus_v2.public.document_form_mappings,postgres.locus_v2.public.document_records,postgres.locus_v2.public.form,postgres.locus_v2.public.form_final_status,postgres.locus_v2.public.form_final_status_lookup,postgres.locus_v2.public.form_team_status_mapping,postgres.locus_v2.public.network_team,postgres.locus_v2.public.project,postgres.locus_v2.public.project_final_status_lookup,postgres.locus_v2.public.status_type,postgres.locus_v2.public.sub_forms,postgres.locus_v2.public.team_final_status,postgres.locus_v2.public.user_data,postgres.locus_v2.public.user_ops_form_mapping,postgres.locus_v2.public.user_re_project_mapping,postgres.locus_v2.public.user_role_mapping,postgres.locus_v2.public.user_roles,postgres.locus_v2.public.user_zone_mapping,postgres.locus_v2.public.user_zones", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "name": "s3_sink.postgres.locus_v2.public.table_group_1.v1"}, "name": "s3_sink.postgres.locus_v2.public.table_group_1.v1"}