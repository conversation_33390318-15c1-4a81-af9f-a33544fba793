{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "mysql.wms.wms.audit_activity,mysql.wms.wms.audit_crate,mysql.wms.wms.audit_crate_item,mysql.wms.wms.audit_job,mysql.wms.wms.audit_job_line_item,mysql.wms.wms.audit_job_log,mysql.wms.wms.audit_user,mysql.wms.wms.audit_user_log,mysql.wms.wms.bad_stock_container,mysql.wms.wms.bad_stock_drop_zone,mysql.wms.wms.bad_stock_inventory_log,mysql.wms.wms.bad_stock_item,mysql.wms.wms.billing_group,mysql.wms.wms.billing_group_order_invoice_tracker,mysql.wms.wms.container_migration_task,mysql.wms.wms.container_migration_zone_map,mysql.wms.wms.dispatch_consignment,mysql.wms.wms.dispatch_container,mysql.wms.wms.dispatch_trip,mysql.wms.wms.dispatch_zone,mysql.wms.wms.employee_activity,mysql.wms.wms.employee_break,mysql.wms.wms.employee_session,mysql.wms.wms.entity_config_mappings,mysql.wms.wms.entity_group,mysql.wms.wms.external_order_activity_invoice_tracker,mysql.wms.wms.handover_qc_activity,mysql.wms.wms.handover_qc_activity_container,mysql.wms.wms.handover_qc_activity_item,mysql.wms.wms.inbound_financial_transaction,mysql.wms.wms.item_audit,mysql.wms.wms.migration_container,mysql.wms.wms.ob_pkg_entity_container_log,mysql.wms.wms.ob_pkg_entity_item,mysql.wms.wms.ob_pkg_entity_item_actual,mysql.wms.wms.ob_pkg_external_order_activity,mysql.wms.wms.ob_pkg_item_ledger,mysql.wms.wms.ob_pkg_outbound_container,mysql.wms.wms.ob_pkg_outbound_container_log,mysql.wms.wms.order,mysql.wms.wms.order_cancellation,mysql.wms.wms.order_cancellation_record,mysql.wms.wms.order_item,mysql.wms.wms.outbound_billing_item,mysql.wms.wms.outbound_config_log,mysql.wms.wms.outbound_container,mysql.wms.wms.outbound_demand,mysql.wms.wms.outbound_invoice,mysql.wms.wms.outbound_item,mysql.wms.wms.packaging_bucket_variant,mysql.wms.wms.packaging_task_item,mysql.wms.wms.packaging_tasks,mysql.wms.wms.pick_list,mysql.wms.wms.pick_list_container_mapping,mysql.wms.wms.pick_list_item,mysql.wms.wms.pick_list_item_container_mapping,mysql.wms.wms.pick_list_item_location_variant_batch,mysql.wms.wms.pick_list_item_log,mysql.wms.wms.pick_list_item_order_mapping,mysql.wms.wms.pick_list_log,mysql.wms.wms.picker_zone_allocation_mapping,mysql.wms.wms.picker_zone_allocation_mapping_log,mysql.wms.wms.picker_zone_preferences,mysql.wms.wms.picker_zone_preferences_log,mysql.wms.wms.picking_slot_demand_details,mysql.wms.wms.picking_slot_demand_details_log,mysql.wms.wms.picking_slot_info,mysql.wms.wms.picking_type_rate,mysql.wms.wms.put_list,mysql.wms.wms.put_list_item,mysql.wms.wms.put_list_item_locations,mysql.wms.wms.put_list_state_transition_log,mysql.wms.wms.putlist_bucket,mysql.wms.wms.putlist_bucket_event,mysql.wms.wms.qc_merchant_score,mysql.wms.wms.qc_outlet_config,mysql.wms.wms.qc_picker_score,mysql.wms.wms.segregation_task,mysql.wms.wms.segregation_task_item,mysql.wms.wms.segregation_task_item_pallet_mapping,mysql.wms.wms.segregation_task_pallet_mapping,mysql.wms.wms.station,mysql.wms.wms.station_log,mysql.wms.wms.unloading_attribution,mysql.wms.wms.unloading_container,mysql.wms.wms.unloading_group,mysql.wms.wms.unloading_group_image,mysql.wms.wms.unloading_proof_of_delivery,mysql.wms.wms.unloading_task_expected_item,mysql.wms.wms.user_attendance,mysql.wms.wms.wave", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "name": "s3_sink.mysql.wms.wms.table_group_2.v1"}, "name": "s3_sink.mysql.wms.wms.table_group_2.v1"}