{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "postgres.pricing_v3.public.attribute_management_allocationgrouprulemap,postgres.pricing_v3.public.attribute_management_attributemasterrule,postgres.pricing_v3.public.attribute_management_brandsfundallocationgroup,postgres.pricing_v3.public.attribute_management_brandsfundapproval,postgres.pricing_v3.public.attribute_management_brandsfundcityapproval,postgres.pricing_v3.public.attribute_management_brandsfundsummary,postgres.pricing_v3.public.attribute_management_brandssheetfile,postgres.pricing_v3.public.attribute_management_itembrandfundagendaattribute,postgres.pricing_v3.public.attribute_management_kvibrandfundagendaattribute,postgres.pricing_v3.public.attribute_management_manufactureremail,postgres.pricing_v3.public.bundles_and_combos_approval,postgres.pricing_v3.public.bundles_and_combos_city_approval,postgres.pricing_v3.public.bundles_and_combos_domain_bundlebrandfundsheetfile,postgres.pricing_v3.public.catalog_domain_kvi_item,postgres.pricing_v3.public.catalog_domain_kvitagging,postgres.pricing_v3.public.competitor_domain_competitorconfiguration,postgres.pricing_v3.public.competitor_domain_competitormaster,postgres.pricing_v3.public.cron_tasks,postgres.pricing_v3.public.event_management_domain_pricingeventerrors,postgres.pricing_v3.public.pricing_domain_city,postgres.pricing_v3.public.pricing_domain_citycluster,postgres.pricing_v3.public.pricing_domain_cmscity,postgres.pricing_v3.public.pricing_domain_outlet,postgres.pricing_v3.public.pricing_domain_pricerecommendation,postgres.pricing_v3.public.pricing_domain_prices,postgres.pricing_v3.public.pricing_domain_product,postgres.pricing_v3.public.pricing_domain_productchangelog,postgres.pricing_v3.public.pricing_domain_superstore,postgres.pricing_v3.public.replication_domain_itemcityreplicationchangelog,postgres.pricing_v3.public.rule_management_agendarulekvialignedrm,postgres.pricing_v3.public.rule_management_agendarulekviinternal,postgres.pricing_v3.public.rule_management_agendarulekvirm,postgres.pricing_v3.public.rule_management_agendarulerm,postgres.pricing_v3.public.rule_management_agendarulermitemid,postgres.pricing_v3.public.rule_management_agendarulesku,postgres.pricing_v3.public.rule_management_kviinternalbenchmarkingladder,postgres.pricing_v3.public.rule_management_masterrule,postgres.pricing_v3.public.rule_management_pricingconfiguration,postgres.pricing_v3.public.rule_management_promotiontags,postgres.pricing_v3.public.rule_management_sheetfile,postgres.pricing_v3.public.rule_management_pricingconfigurationsheetfile", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,filterDelete", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "transforms.filterDelete.type": "io.confluent.connect.transforms.Filter$Value", "transforms.filterDelete.filter.type": "exclude", "transforms.filterDelete.filter.condition": "[?(@.op=='d' && @.source.table in ['pricing_domain_productchangelog', 'pricing_domain_product', 'rule_management_masterrule'])]", "transforms.filterDelete.missing.or.null.behavior": "exclude", "name": "s3_sink.postgres.pricing_v3.public.table_group_1.v2"}, "name": "s3_sink.postgres.pricing_v3.public.table_group_1.v2"}