{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "postgres.ops_management.public.applied_business_config,postgres.ops_management.public.business_config_template,postgres.ops_management.public.employee_tenant_map,postgres.ops_management.public.employee_warnings,postgres.ops_management.public.external_event,postgres.ops_management.public.job_application,postgres.ops_management.public.job_vacancy,postgres.ops_management.public.leaderboard_ledger,postgres.ops_management.public.leaderboard_projection,postgres.ops_management.public.leave_actions,postgres.ops_management.public.leave_balance,postgres.ops_management.public.leaves,postgres.ops_management.public.referral_leads,postgres.ops_management.public.selfie_verification_attempts,postgres.ops_management.public.training,postgres.ops_management.public.training_challenge,postgres.ops_management.public.training_component,postgres.ops_management.public.training_component_translation,postgres.ops_management.public.training_level,postgres.ops_management.public.training_level_mapping,postgres.ops_management.public.training_user_activity,postgres.ops_management.public.training_user_progress,postgres.ops_management.public.user_bank_details,postgres.ops_management.public.user_milestone,postgres.ops_management.public.user_skill,postgres.ops_management.public.user_zone", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "name": "s3_sink.postgres.ops_management.public.table_group_2.v1"}, "name": "s3_sink.postgres.ops_management.public.table_group_2.v1"}