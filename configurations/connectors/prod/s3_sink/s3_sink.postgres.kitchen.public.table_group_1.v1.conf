{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "postgres.kitchen.public.activity_logs,postgres.kitchen.public.attendance_summary,postgres.kitchen.public.dish,postgres.kitchen.public.entity,postgres.kitchen.public.entity_schedule,postgres.kitchen.public.foodware_mapping,postgres.kitchen.public.ingredient,postgres.kitchen.public.item_mapping,postgres.kitchen.public.order_states,postgres.kitchen.public.orders,postgres.kitchen.public.packaging_bags,postgres.kitchen.public.preparation_task_log,postgres.kitchen.public.product,postgres.kitchen.public.product_mapping,postgres.kitchen.public.product_transformation,postgres.kitchen.public.return_order,postgres.kitchen.public.roster_planner,postgres.kitchen.public.shift_record,postgres.kitchen.public.station,postgres.kitchen.public.station_product_mapping,postgres.kitchen.public.store_product_mapping,postgres.kitchen.public.store_station,postgres.kitchen.public.sub_order,postgres.kitchen.public.sub_order_item,postgres.kitchen.public.subdish,postgres.kitchen.public.user_entity_mapping", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "name": "s3_sink.postgres.kitchen.public.table_group_1.v1"}, "name": "s3_sink.postgres.kitchen.public.table_group_1.v1"}