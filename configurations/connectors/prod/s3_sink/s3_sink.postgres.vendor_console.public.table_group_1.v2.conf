{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "postgres.vendor_console.public.appointment,postgres.vendor_console.public.appointment_capacity_config,postgres.vendor_console.public.appointment_capacity_config_log,postgres.vendor_console.public.appointment_facility_day_capacity,postgres.vendor_console.public.appointment_facility_slot,postgres.vendor_console.public.appointment_facility_slot_log,postgres.vendor_console.public.appointment_log,postgres.vendor_console.public.appointment_po_mapping,postgres.vendor_console.public.appointment_slot_config,postgres.vendor_console.public.appointment_slot_config_log,postgres.vendor_console.public.appointment_slot_mapping,postgres.vendor_console.public.appointment_slot_mapping_log,postgres.vendor_console.public.auth_plan,postgres.vendor_console.public.base_bulk_upload_request_tracker,postgres.vendor_console.public.client_po_details,postgres.vendor_console.public.client_po_items,postgres.vendor_console.public.cms_assortment_request,postgres.vendor_console.public.cms_assortment_request_log,postgres.vendor_console.public.courier_partner_days_config,postgres.vendor_console.public.courier_partner_details,postgres.vendor_console.public.day_capacity_request_data_tracker,postgres.vendor_console.public.entity,postgres.vendor_console.public.entity_plan_mapping,postgres.vendor_console.public.facility_config,postgres.vendor_console.public.facility_config_log,postgres.vendor_console.public.facility_day_level_capacity,postgres.vendor_console.public.facility_day_level_capacity_log,postgres.vendor_console.public.facility_slot_time,postgres.vendor_console.public.facility_slot_time_log,postgres.vendor_console.public.invoice,postgres.vendor_console.public.invoice_event_log,postgres.vendor_console.public.invoice_payment_details,postgres.vendor_console.public.invoice_payment_mapping,postgres.vendor_console.public.item_proxy_category,postgres.vendor_console.public.po_reservation,postgres.vendor_console.public.po_reservation_log,postgres.vendor_console.public.report_requests,postgres.vendor_console.public.reservation_slot_details,postgres.vendor_console.public.reservation_slot_details_log,postgres.vendor_console.public.shipment,postgres.vendor_console.public.slot_capacity,postgres.vendor_console.public.slot_capacity_config,postgres.vendor_console.public.slot_capacity_log,postgres.vendor_console.public.slot_capacity_request_data_tracker,postgres.vendor_console.public.user_details,postgres.vendor_console.public.user_entity_mapping,postgres.vendor_console.public.vendor_facility_auto_release", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "name": "s3_sink.postgres.vendor_console.public.table_group_1.v2"}, "name": "s3_sink.postgres.vendor_console.public.table_group_1.v2"}