{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "postgres.seller.public.apob_facility,postgres.seller.public.campaigns,postgres.seller.public.cart,postgres.seller.public.cart_order_mapping,postgres.seller.public.cart_transaction_log,postgres.seller.public.faq,postgres.seller.public.item_outlet_inventory,postgres.seller.public.rate_card_item,postgres.seller.public.recall_request,postgres.seller.public.recall_request_batch,postgres.seller.public.recall_request_batch_item,postgres.seller.public.recall_request_batch_log,postgres.seller.public.recall_request_batch_pro_detail,postgres.seller.public.sales_summary,postgres.seller.public.scheduled_jobs,postgres.seller.public.seller,postgres.seller.public.seller_advertiser_mapping,postgres.seller.public.seller_apob,postgres.seller.public.seller_brand_detail,postgres.seller.public.seller_document_info,postgres.seller.public.seller_order_level_pos_sales_info,postgres.seller.public.seller_order_level_sales_info,postgres.seller.public.seller_payout_commission,postgres.seller.public.seller_product_mappings,postgres.seller.public.seller_product_request_mappings,postgres.seller.public.seller_t_and_c,postgres.seller.public.task,postgres.seller.public.user_seller_mapping,postgres.seller.public.workflow", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "name": "s3_sink.postgres.seller.public.table_group_1.v1"}, "name": "s3_sink.postgres.seller.public.table_group_1.v1"}