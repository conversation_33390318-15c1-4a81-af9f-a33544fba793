{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "mysql.warehouse_location.warehouse_location.batch_logic_log,mysql.warehouse_location.warehouse_location.billing_entity_crate_mapping,mysql.warehouse_location.warehouse_location.billing_entity_invoice_mapping,mysql.warehouse_location.warehouse_location.case_variant_entity_mapping,mysql.warehouse_location.warehouse_location.case_variant_entity_state_log,mysql.warehouse_location.warehouse_location.item_zone_mapping,mysql.warehouse_location.warehouse_location.item_zone_mapping_log,mysql.warehouse_location.warehouse_location.label_product_mapping,mysql.warehouse_location.warehouse_location.label_product_mapping_log,mysql.warehouse_location.warehouse_location.leaderboard_metrics,mysql.warehouse_location.warehouse_location.migration_task,mysql.warehouse_location.warehouse_location.migration_task_activity,mysql.warehouse_location.warehouse_location.migration_task_location_log,mysql.warehouse_location.warehouse_location.migration_task_state_log,mysql.warehouse_location.warehouse_location.pick_list_item_crate_mapping,mysql.warehouse_location.warehouse_location.warehouse_aisle_rack_type,mysql.warehouse_location.warehouse_location.warehouse_picking_zone,mysql.warehouse_location.warehouse_location.warehouse_picklist_to_crate_mapping,mysql.warehouse_location.warehouse_location.warehouse_picklist_to_irt_picklist_mapping,mysql.warehouse_location.warehouse_location.warehouse_storage_location_log", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "name": "s3_sink.mysql.warehouse_location.warehouse_location.table_group_3.v2"}, "name": "s3_sink.mysql.warehouse_location.warehouse_location.table_group_3.v2"}