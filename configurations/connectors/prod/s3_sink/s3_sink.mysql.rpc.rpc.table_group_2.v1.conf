{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "mysql.rpc.rpc.ams_city_cluster_mapping,mysql.rpc.rpc.ams_cluster,mysql.rpc.rpc.attribute_outlet_entity_vendor,mysql.rpc.rpc.dated_tot_margin,mysql.rpc.rpc.facility_adjacency,mysql.rpc.rpc.facility_adjacency_log,mysql.rpc.rpc.facility_polygon,mysql.rpc.rpc.facility_polygon_log,mysql.rpc.rpc.item_tag_type,mysql.rpc.rpc.item_tag_type_value_mapping,mysql.rpc.rpc.new_store_assortment_request,mysql.rpc.rpc.new_store_assortment_request_log,mysql.rpc.rpc.off_invoice_rule,mysql.rpc.rpc.off_invoice_rule_claim,mysql.rpc.rpc.off_invoice_rule_instance,mysql.rpc.rpc.off_invoice_rule_target,mysql.rpc.rpc.off_invoice_rule_update_request,mysql.rpc.rpc.off_invoice_target_claim_entity,mysql.rpc.rpc.product_product_log,mysql.rpc.rpc.return_policy,mysql.rpc.rpc.return_policy_bucket_attribute,mysql.rpc.rpc.substitutable_group,mysql.rpc.rpc.substitutable_item_group,mysql.rpc.rpc.supply_event,mysql.rpc.rpc.supply_event_info,mysql.rpc.rpc.temporary_stock_details,mysql.rpc.rpc.temporary_stock_details_log,mysql.rpc.rpc.transfer_case_size,mysql.rpc.rpc.transfer_case_size_log,mysql.rpc.rpc.transfer_tag_rules,mysql.rpc.rpc.transfer_tag_rules_log,mysql.rpc.rpc.warehouse_transition", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "name": "s3_sink.mysql.rpc.rpc.table_group_2.v1"}, "name": "s3_sink.mysql.rpc.rpc.table_group_2.v1"}