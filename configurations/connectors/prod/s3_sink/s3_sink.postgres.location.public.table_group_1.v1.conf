{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "postgres.location.public.layout_component,postgres.location.public.legend_mapping_detail,postgres.location.public.location,postgres.location.public.location_component_location_type_mapping,postgres.location.public.location_components_config,postgres.location.public.location_migration_mapping,postgres.location.public.location_nomenclature,postgres.location.public.location_type,postgres.location.public.location_zone_mapping,postgres.location.public.location_zone_rule,postgres.location.public.outlet_layout,postgres.location.public.outlet_location,postgres.location.public.outlet_zone,postgres.location.public.zone,postgres.location.public.zone_entity_rule", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "name": "s3_sink.postgres.location.public.table_group_1.v1"}, "name": "s3_sink.postgres.location.public.table_group_1.v1"}