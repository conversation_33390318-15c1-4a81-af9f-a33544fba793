{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "postgres.grofers_db_new1.public.gr_crm_order_event_message_mapping,postgres.grofers_db_new1.public.gr_gstin,postgres.grofers_db_new1.public.gr_order_cancellation_reason_mapping,postgres.grofers_db_new1.public.gr_payment_summary,postgres.grofers_db_new1.public.gr_product_list,postgres.grofers_db_new1.public.gr_station,postgres.grofers_db_new1.public.gr_station_deliverer_mapping,postgres.grofers_db_new1.public.gr_station_merchant_mapping,postgres.grofers_db_new1.public.gr_store_merchant_mapping,postgres.grofers_db_new1.public.gr_support_user_response,postgres.grofers_db_new1.public.gr_temp_user,postgres.grofers_db_new1.public.gr_user,postgres.grofers_db_new1.public.gr_user_address_mapping,postgres.grofers_db_new1.public.gr_user_deliverer,postgres.grofers_db_new1.public.gr_user_gstin_mapping,postgres.grofers_db_new1.public.gr_wallet,postgres.grofers_db_new1.public.gr_wallet_cash_type,postgres.grofers_db_new1.public.gr_wallet_history,postgres.grofers_db_new1.public.gr_wallet_transaction,postgres.grofers_db_new1.public.push_notifications_apnsdevice,postgres.grofers_db_new1.public.push_notifications_gcmdevice", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "name": "s3_sink.postgres.grofers_db_new1.public.table_group_1.v2"}, "name": "s3_sink.postgres.grofers_db_new1.public.table_group_1.v2"}