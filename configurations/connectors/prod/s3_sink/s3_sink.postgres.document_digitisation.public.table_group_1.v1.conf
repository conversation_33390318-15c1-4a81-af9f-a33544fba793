{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "postgres.document_digitisation.public.auth_user,postgres.document_digitisation.public.digitisation_invoice_mapping_v2,postgres.document_digitisation.public.digitisation_request,postgres.document_digitisation.public.digitisation_request_action,postgres.document_digitisation.public.digitise_bulk_invoice_request,postgres.document_digitisation.public.digitise_invoice_request,postgres.document_digitisation.public.digitised_document,postgres.document_digitisation.public.digitised_document_log,postgres.document_digitisation.public.digitised_invoice_data,postgres.document_digitisation.public.digitised_invoice_item_data,postgres.document_digitisation.public.discrepancy_note_data,postgres.document_digitisation.public.discrepancy_note_data_dump,postgres.document_digitisation.public.grn_data,postgres.document_digitisation.public.invoice_verification_request,postgres.document_digitisation.public.purchase_item_data,postgres.document_digitisation.public.purchase_order_data,postgres.document_digitisation.public.seller,postgres.document_digitisation.public.tenant,postgres.document_digitisation.public.user_management_user_group,postgres.document_digitisation.public.user_seller_map,postgres.document_digitisation.public.user_tenant_map,postgres.document_digitisation.public.verification_request,postgres.document_digitisation.public.verification_request_step,postgres.document_digitisation.public.verification_step,postgres.document_digitisation.public.verification_trail,postgres.document_digitisation.public.verified_invoice_data,postgres.document_digitisation.public.verified_invoice_data_log,postgres.document_digitisation.public.verified_invoice_items_data,postgres.document_digitisation.public.verified_invoice_items_data_log", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "name": "s3_sink.postgres.document_digitisation.public.table_group_1.v1"}, "name": "s3_sink.postgres.document_digitisation.public.table_group_1.v1"}