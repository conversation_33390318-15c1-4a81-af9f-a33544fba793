{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "postgres.search_ranking.public.autosuggest_entity_ranking,postgres.search_ranking.public.city_info,postgres.search_ranking.public.component,postgres.search_ranking.public.container,postgres.search_ranking.public.container_component_mapping,postgres.search_ranking.public.dump_products,postgres.search_ranking.public.entity_city_metrics,postgres.search_ranking.public.entity_container_mapping,postgres.search_ranking.public.keyterm_product_id_mapping,postgres.search_ranking.public.keyterm_ptype_mappings,postgres.search_ranking.public.newly_launched_products,postgres.search_ranking.public.saleability_city_level_details_v1,postgres.search_ranking.public.saleability_pan_india_map_v1,postgres.search_ranking.public.search_atc_data,postgres.search_ranking.public.search_keyterms,postgres.search_ranking.public.search_spellcorrect_map,postgres.search_ranking.public.sr_keyword_count,postgres.search_ranking.public.transliteration,postgres.search_ranking.public.usecase,postgres.search_ranking.public.usecase_item,postgres.search_ranking.public.usecase_item_data,postgres.search_ranking.public.usecase_item_section,postgres.search_ranking.public.usecase_item_section_mapping,postgres.search_ranking.public.usecase_tag,postgres.search_ranking.public.usecase_tag_mapping", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "name": "s3_sink.postgres.search_ranking.public.table_group_1.v2"}, "name": "s3_sink.postgres.search_ranking.public.table_group_1.v2"}