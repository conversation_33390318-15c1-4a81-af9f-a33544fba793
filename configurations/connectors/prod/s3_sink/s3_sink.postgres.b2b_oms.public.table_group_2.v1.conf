{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "postgres.b2b_oms.public.consignment,postgres.b2b_oms.public.consignment_container_mapping,postgres.b2b_oms.public.consignment_container_status_log,postgres.b2b_oms.public.consignment_invoice_mapping,postgres.b2b_oms.public.consignment_item,postgres.b2b_oms.public.consignment_status_log,postgres.b2b_oms.public.consignment_transaction_entity", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "name": "s3_sink.postgres.b2b_oms.public.table_group_2.v1"}, "name": "s3_sink.postgres.b2b_oms.public.table_group_2.v1"}