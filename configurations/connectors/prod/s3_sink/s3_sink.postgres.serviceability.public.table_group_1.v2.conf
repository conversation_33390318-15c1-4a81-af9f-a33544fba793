{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "postgres.serviceability.public.ser_disruption_request,postgres.serviceability.public.ser_disruption_schedule,postgres.serviceability.public.ser_distance_based_surge_logs,postgres.serviceability.public.ser_entity_metrics,postgres.serviceability.public.ser_node,postgres.serviceability.public.ser_polygon_refresh_logs,postgres.serviceability.public.ser_polygons,postgres.serviceability.public.ser_rain_events,postgres.serviceability.public.ser_store_config,postgres.serviceability.public.ser_store_config_change_logs,postgres.serviceability.public.ser_store_polygon_logs,postgres.serviceability.public.ser_store_polygon_schedule,postgres.serviceability.public.ser_store_polygons,postgres.serviceability.public.surge_di_based_with_mov", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,filterDelete", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "transforms.filterDelete.type": "io.confluent.connect.transforms.Filter$Value", "transforms.filterDelete.filter.condition": "[?(@.op=='d' && @.source.table in [ser_entity_metrics])]", "transforms.filterDelete.missing.or.null.behavior": "exclude", "transforms.filterDelete.filter.type": "exclude", "name": "s3_sink.postgres.serviceability.public.table_group_1.v2"}, "name": "s3_sink.postgres.serviceability.public.table_group_1.v2"}