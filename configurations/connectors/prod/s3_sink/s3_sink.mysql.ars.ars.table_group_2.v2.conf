{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "s3.part.size": "10214400", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "mysql.ars.ars.ars_job_run,mysql.ars.ars.backend_facility_transfer_attributes,mysql.ars.ars.case_incorporation_result,mysql.ars.ars.error_details,mysql.ars.ars.final_indent,mysql.ars.ars.frontend_cycle_sto_quantity,mysql.ars.ars.iov_buffer_dois,mysql.ars.ars.item_details,mysql.ars.ars.item_min_max_quantity,mysql.ars.ars.item_min_max_quantity_log,mysql.ars.ars.item_outlet_errors,mysql.ars.ars.item_outlet_vendor_price_data,mysql.ars.ars.job_run,mysql.ars.ars.job_run_count,mysql.ars.ars.job_run_location,mysql.ars.ars.job_run_snapshot,mysql.ars.ars.min_bump_algo_result,mysql.ars.ars.ordering_logic_result,mysql.ars.ars.outlet,mysql.ars.ars.outlet_item_aps_derived_cpd,mysql.ars.ars.outlet_item_cpd,mysql.ars.ars.outlet_item_open_po,mysql.ars.ars.outlet_item_universe,mysql.ars.ars.outlet_vendor_item_tat_days,mysql.ars.ars.po_cycle_type_attributes,mysql.ars.ars.table_updates,mysql.ars.ars.triggering_logic_result", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,filterDelete", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "transforms.filterDelete.type": "io.confluent.connect.transforms.Filter$Value", "transforms.filterDelete.filter.condition": "[?(@.op=='d' && @.source.table in ['outlet_item_cpd', 'city_item_alignment'])]", "transforms.filterDelete.missing.or.null.behavior": "exclude", "transforms.filterDelete.filter.type": "exclude", "name": "s3_sink.mysql.ars.ars.table_group_2.v2"}, "name": "s3_sink.mysql.ars.ars.table_group_2.v2"}