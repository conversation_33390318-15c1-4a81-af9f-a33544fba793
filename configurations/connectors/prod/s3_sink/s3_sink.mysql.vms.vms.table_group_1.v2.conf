{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "s3.part.size": "10214400", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "mysql.vms.vms.contact_address,mysql.vms.vms.contact_electronic,mysql.vms.vms.contact_model,mysql.vms.vms.contact_phone,mysql.vms.vms.contact_purpose,mysql.vms.vms.contact_type,mysql.vms.vms.contact_vendor,mysql.vms.vms.delivery_type,mysql.vms.vms.external_api_call_logs,mysql.vms.vms.facility_vendor_address,mysql.vms.vms.io_max_doi,mysql.vms.vms.io_max_doi_log,mysql.vms.vms.iov_fill_rate_buffer_doi,mysql.vms.vms.item_facility_group_mapping,mysql.vms.vms.item_facility_group_mapping_log,mysql.vms.vms.load_group_attributes,mysql.vms.vms.load_group_attributes_log,mysql.vms.vms.po_cycle,mysql.vms.vms.po_cycle_types,mysql.vms.vms.vendor_approval,mysql.vms.vms.vendor_config,mysql.vms.vms.vendor_config_log,mysql.vms.vms.vendor_contact_address,mysql.vms.vms.vendor_item_physical_facility_attributes,mysql.vms.vms.vendor_item_physical_facility_attributes_log,mysql.vms.vms.vms_company,mysql.vms.vms.vms_document_type,mysql.vms.vms.vms_line_of_business,mysql.vms.vms.vms_manufacturer_contact_details,mysql.vms.vms.vms_ownership_types,mysql.vms.vms.vms_vendor,mysql.vms.vms.vms_vendor_alignment,mysql.vms.vms.vms_vendor_alignment_log,mysql.vms.vms.vms_vendor_alignment_state,mysql.vms.vms.vms_vendor_city_documents,mysql.vms.vms.vms_vendor_city_mapping,mysql.vms.vms.vms_vendor_city_product_details,mysql.vms.vms.vms_vendor_city_tax_info,mysql.vms.vms.vms_vendor_facility_alignment,mysql.vms.vms.vms_vendor_facility_alignment_log,mysql.vms.vms.vms_vendor_group_mapping,mysql.vms.vms.vms_vendor_merchant_mapping,mysql.vms.vms.vms_vendor_new_pi_logic,mysql.vms.vms.vms_vendor_new_pi_logic_log,mysql.vms.vms.vms_vendor_pi_logic", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "name": "s3_sink.mysql.vms.vms.table_group_1.v2"}, "name": "s3_sink.mysql.vms.vms.table_group_1.v2"}