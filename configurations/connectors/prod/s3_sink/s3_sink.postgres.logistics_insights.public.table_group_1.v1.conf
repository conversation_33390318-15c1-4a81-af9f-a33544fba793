{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "postgres.logistics_insights.public.base_model_csvs,postgres.logistics_insights.public.base_sla_metrics,postgres.logistics_insights.public.base_sla_model_runs,postgres.logistics_insights.public.carriers,postgres.logistics_insights.public.cities,postgres.logistics_insights.public.cron_executions,postgres.logistics_insights.public.files,postgres.logistics_insights.public.input_filters,postgres.logistics_insights.public.input_groups,postgres.logistics_insights.public.order_projections,postgres.logistics_insights.public.roles,postgres.logistics_insights.public.slots,postgres.logistics_insights.public.snapshot_inputs,postgres.logistics_insights.public.snapshots,postgres.logistics_insights.public.stores,postgres.logistics_insights.public.tags,postgres.logistics_insights.public.user_runs,postgres.logistics_insights.public.users", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "name": "s3_sink.postgres.logistics_insights.public.table_group_1.v1"}, "name": "s3_sink.postgres.logistics_insights.public.table_group_1.v1"}