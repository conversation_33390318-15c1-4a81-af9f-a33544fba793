{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "postgres.fleet_management.public.fleet_management_approval_request,postgres.fleet_management.public.fleet_management_device,postgres.fleet_management.public.fleet_management_distance_mapping,postgres.fleet_management.public.fleet_management_node_billable_city_mapping,postgres.fleet_management.public.fleet_management_predicate,postgres.fleet_management.public.fleet_management_rule,postgres.fleet_management.public.fleet_management_tolls_cost,postgres.fleet_management.public.fleet_management_transaction_logs,postgres.fleet_management.public.fleet_management_trip_adhoc_costs_attributes,postgres.fleet_management.public.fleet_management_trips_cost,postgres.fleet_management.public.fleet_management_truck_management,postgres.fleet_management.public.fleet_management_truck_vendor_mapping,postgres.fleet_management.public.fleet_management_user,postgres.fleet_management.public.fleet_management_user_address,postgres.fleet_management.public.fleet_management_user_detail,postgres.fleet_management.public.fleet_management_user_detail_store_mapping,postgres.fleet_management.public.fleet_management_user_document,postgres.fleet_management.public.fleet_management_vehicle,postgres.fleet_management.public.fleet_management_vehicle_detail,postgres.fleet_management.public.fleet_management_vendor,postgres.fleet_management.public.fleet_management_vendor_rate_card_config,postgres.fleet_management.public.fleet_management_vendor_rate_card_config_rule_mapping", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "name": "s3_sink.postgres.fleet_management.public.table_group_1.v2"}, "name": "s3_sink.postgres.fleet_management.public.table_group_1.v2"}