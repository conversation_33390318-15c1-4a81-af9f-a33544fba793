{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "postgres.emergency_services.public.ambulance,postgres.emergency_services.public.ambulance_checklist_item_update,postgres.emergency_services.public.ambulance_checklist_session,postgres.emergency_services.public.ambulance_order_trip_mapping,postgres.emergency_services.public.ambulance_staff_evaluation,postgres.emergency_services.public.ambulance_trip,postgres.emergency_services.public.ambulance_trip_event,postgres.emergency_services.public.ambulance_trip_staff,postgres.emergency_services.public.consumable,postgres.emergency_services.public.consumable_batch,postgres.emergency_services.public.depot,postgres.emergency_services.public.medical_remark,postgres.emergency_services.public.medical_staff,postgres.emergency_services.public.patient_details,postgres.emergency_services.public.shift", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "name": "s3_sink.postgres.emergency_services.public.table_group_1.v1"}, "name": "s3_sink.postgres.emergency_services.public.table_group_1.v1"}