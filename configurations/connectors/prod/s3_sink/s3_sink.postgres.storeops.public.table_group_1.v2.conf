{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "postgres.storeops.public.activity,postgres.storeops.public.asset_tracking,postgres.storeops.public.asset_tracking_log,postgres.storeops.public.business_eventdb,postgres.storeops.public.consignment_metrics,postgres.storeops.public.dbasync_task,postgres.storeops.public.discrepancy_logs,postgres.storeops.public.er,postgres.storeops.public.er_container,postgres.storeops.public.er_container_item,postgres.storeops.public.erline,postgres.storeops.public.inventory_update_log,postgres.storeops.public.inward_stock_attribute,postgres.storeops.public.item_activity,postgres.storeops.public.item_details,postgres.storeops.public.item_exceptions,postgres.storeops.public.item_site_tag_mapping,postgres.storeops.public.location,postgres.storeops.public.location_inventory,postgres.storeops.public.order_complaint,postgres.storeops.public.packaging_suggestion_details,postgres.storeops.public.poindent,postgres.storeops.public.poline,postgres.storeops.public.product_exception,postgres.storeops.public.product_info,postgres.storeops.public.site,postgres.storeops.public.user_details", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "name": "s3_sink.postgres.storeops.public.table_group_1.v2"}, "name": "s3_sink.postgres.storeops.public.table_group_1.v2"}