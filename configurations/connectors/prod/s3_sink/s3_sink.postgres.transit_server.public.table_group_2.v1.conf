{"config": {"avro.codec": "snappy", "connector.class": "io.confluent.connect.s3.S3SinkConnector", "flush.size": "100000", "format.class": "io.confluent.connect.s3.format.avro.AvroFormat", "locale": "en-US", "partition.duration.ms": "1800000", "partitioner.class": "io.confluent.connect.storage.partitioner.TimeBasedPartitioner", "path.format": "'year'=YYYY/'month'=MM/'day'=dd/'hour'=HH", "rotate.schedule.interval.ms": "600000", "s3.bucket.name": "prod-data-debezium-raw", "s3.region": "ap-southeast-1", "schema.compatibility": "FULL", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "tasks.max": "1", "timestamp.extractor": "Record", "timezone": "UTC", "topics": "postgres.transit_server.public.transit_allocation_fulfillment_queue,postgres.transit_server.public.transit_courier_order,postgres.transit_server.public.transit_courier_order_log,postgres.transit_server.public.transit_delivery_challan,postgres.transit_server.public.transit_delivery_challan_item,postgres.transit_server.public.transit_discrepancy_notification_mapping,postgres.transit_server.public.transit_ewaybill,postgres.transit_server.public.transit_metrics,postgres.transit_server.public.transit_metrics_group,postgres.transit_server.public.transit_metrics_group_v2,postgres.transit_server.public.transit_metrics_v2,postgres.transit_server.public.transit_node_mapping,postgres.transit_server.public.transit_notification,postgres.transit_server.public.transit_notification_user_mapping,postgres.transit_server.public.transit_projection_dispatch_demand,postgres.transit_server.public.transit_projection_plan_vehicle,postgres.transit_server.public.transit_projection_plan_vehicle_destination,postgres.transit_server.public.transit_projection_trip_duration,postgres.transit_server.public.transit_task_node_detail,postgres.transit_server.public.transit_travel_segment,postgres.transit_server.public.transit_user_group,postgres.transit_server.public.transit_user_group_view_mapping,postgres.transit_server.public.transit_user_profile_group,postgres.transit_server.public.transit_user_session", "topics.dir": "", "transforms": "tombstone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transforms.tombstoneHandlerValue.type": "io.confluent.connect.transforms.TombstoneHandler", "transforms.tombstoneHandlerValue.behavior": "ignore", "name": "s3_sink.postgres.transit_server.public.table_group_2.v1"}, "name": "s3_sink.postgres.transit_server.public.table_group_2.v1"}