{"config": {"heartbeat.interval.ms": "60000", "table.include.list": "dbo.TblCityMaster,dbo.UserAttendance,dbo.Userdetail,dbo.tblDepartment,dbo.tblEmployeeShiftMaster,dbo.tblLocation,dbo.tblOutWorkRegister,dbo.tblShiftMaster,dbo.tblemployee,dbo.tbltimeregister,dbo.tbluser", "message.key.columns": "dbo.TblCityMaster:City_Code;dbo.UserAttendance:DeviceID,UserID,AttDateTime;dbo.Userdetail:DeviceID,UserID;dbo.tblDepartment:DepartmentCode,CompanyCode;dbo.tblEmployeeShiftMaster:CARDNO,SSN;dbo.tblLocation:LocationCode,COMPANYCODE;dbo.tblOutWorkRegister:PAYCODE,DateOFFICE,SSN;dbo.tblShiftMaster:SHIFT,CompanyCode;dbo.tblemployee:PRESENTCARDNO,SSN;dbo.tbltimeregister:DateOFFICE,SSN;dbo.tbluser:SSN", "name": "sqlserver.EveProd.dbo.snapshot_20250415.v1", "snapshot.mode": "initial_only", "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092", "connector.class": "io.debezium.connector.sqlserver.SqlServerConnector", "database.server.name": "sqlserver.<PERSON><PERSON><PERSON>", "database.history.kafka.topic": "db_history.sqlserver.EveProd.table_group_1", "database.dbname": "<PERSON><PERSON><PERSON>", "include.schema.changes": "true", "producer.override.batch.size": "327680", "database.history.skip.unparseable.ddl": "true", "tombstones.on.delete": "false"}, "name": "sqlserver.EveProd.dbo.snapshot_20250415.v1"}