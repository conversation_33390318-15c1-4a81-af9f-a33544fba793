#!/usr/bin/env python3
import os
import sys
import hvac

def get_database_credentials(database_name, db_type="postgres"):
    VAULT_URL = 'https://vault-ui-prod.grofer.io'

    # Define possible vault paths to try
    if db_type == "postgres":
        vault_paths = [
            f"data/services/source-replication/kafka-connect/databases/postgres/{database_name}/{database_name}_application_srp_aurora",
            f"data/services/source-replication/kafka-connect/databases/postgres/{database_name}/{database_name}_application_srp",
            f"data/services/source-replication/kafka-connect/databases/postgres/{database_name}/{database_name}_reader_srp_connect_aurora",
            f"data/services/source-replication/kafka-connect/databases/postgres/{database_name}/{database_name}_reader_srp_connect"
        ]
    else:
        vault_paths = [
            f"data/services/source-replication/kafka-connect/databases/mysql/{database_name}/{database_name}_application_srp_aurora",
            f"data/services/source-replication/kafka-connect/databases/mysql/{database_name}/{database_name}_application_srp"
        ]

    github_token = os.environ.get('VAULT_AUTH_GITHUB_TOKEN')
    if not github_token:
        raise ValueError("VAULT_AUTH_GITHUB_TOKEN environment variable not found")

    client = hvac.Client(url=VAULT_URL)
    client.auth.github.login(token=github_token)

    if not client.is_authenticated():
        raise ValueError("Failed to authenticate to Vault")

    # Try each vault path until one works
    last_error = None
    permission_errors = []

    for vault_path in vault_paths:
        try:
            response = client.read(path=vault_path)
            if response and 'data' in response:
                print(f"Successfully found credentials at: {vault_path}")
                return response['data']
        except Exception as e:
            last_error = e
            if "permission denied" in str(e).lower():
                permission_errors.append(vault_path)
                print(f"Found vault path but permission denied: {vault_path}")
            continue

    # If no path worked, raise an error with all attempted paths
    attempted_paths = '\n'.join([f"  - {path}" for path in vault_paths])
    error_msg = f"No data found at any of the attempted vault paths:\n{attempted_paths}"

    if permission_errors:
        error_msg += f"\n\nFound these paths but got permission denied:\n"
        error_msg += '\n'.join([f"  - {path}" for path in permission_errors])
        error_msg += "\n\nPlease check if your VAULT_AUTH_GITHUB_TOKEN has access to these paths."

    error_msg += f"\n\nLast error: {last_error}"
    raise ValueError(error_msg)

if __name__ == "__main__":
    if len(sys.argv) < 2 or len(sys.argv) > 3:
        print("Usage: python3 get_db_creds.py <database_name> [db_type]")
        print("Example: python3 get_db_creds.py seller postgres")
        print("Example: python3 get_db_creds.py ars mysql")
        print("Default db_type is postgres")
        sys.exit(1)

    database_name = sys.argv[1]
    db_type = sys.argv[2] if len(sys.argv) == 3 else "postgres"

    try:
        creds = get_database_credentials(database_name, db_type)
        for key, value in creds.items():
            print(f"{key}: {value}")
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
