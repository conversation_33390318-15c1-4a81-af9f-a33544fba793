
# SRP Table Onboarding Guide

## Overview
This document provides comprehensive step-by-step instructions for onboarding a new table into the SRP (Source Replication Platform) system. Follow these steps exactly to ensure proper integration.

## Prerequisites
- Access to `srp-scripts` repository
- `tinker` CLI tool installed
- `VAULT_AUTH_GITHUB_TOKEN` environment variable set
- Database access permissions
- Table catalog information

## Required Information
Before starting, collect:
- Database name and type (MySQL/PostgreSQL)
- Table name and schema
- Primary key column name
- Partition column (if applicable)
- Table description and owner information

## IMPORTANT 
- you cannot connect to the database and run queries ask the user for that
- you cannot run tinker connector commands ask the user to run them
- rest all the commands scripts can be triggered by you 


## Step 1: Environment Setup

### 1.1 Verify Prerequisites
```bash
# Check vault token
printenv | grep VAULT_AUTH_GITHUB_TOKEN

# Verify tinker installation
which tinker

# Navigate to repository
cd /path/to/srp-scripts
```

### 1.2  Database Credentials Script
Understand the get_db_creds.py script and how it works and run it to get the db creds.
  

Run the script:
```bash
python3 get_db_creds.py <database_name>
```

ALWAYS GIVE USER THE FINAL COMMAND THAT USER CAN RUN TO CONNECT TO CLI 
FOR THE DATABASE STEP GIVE USER THE COMMAND TO CONNECT TO DATABASE AND RUN THE RELEVANT QUERIES
WAIT FOR THE USER TO RUN THE QUERIES AND GIVE CORRECT OUTPUT
PLEASE MAKE SURE TO GENERATE CORRECT QUERIES FOR POSTGRES AND MYSQL 
ALSO GIVE A SINGLE COMMAND TO RUN ALL THE QUERIES AT ONCE
ALSO SPECIFY THE DATABASE IN THIS COMMAND SO THAT USER CAN CONNECT TO THE CORRECT

## Step 2: Database Verification 

### 2.1 Connect to Database
Use the credentials from Step 1.2 to connect:

**For MySQL:**
```bash
mysql -h HOST -P PORT -u USER -p DATABASE_NAME
```

**For PostgreSQL:**
```bash
psql -h HOST -p PORT -U USER -d DATABASE_NAME
```

### 2.2 Verify Table Structure

**For MySQL databases, run this single command:**
```sql
USE database_name; DESCRIBE table_name; SELECT COLUMN_NAME, DATA_TYPE, COLUMN_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'database_name' AND TABLE_NAME = 'table_name' AND DATA_TYPE = 'tinyint'; SHOW KEYS FROM table_name WHERE Key_name = 'PRIMARY'; SELECT * FROM information_schema.columns WHERE table_schema = 'database_name' AND table_name = 'table_name' AND data_type in ('numeric', 'decimal', 'decimal unsigned', 'dec unsigned'); SELECT c.ORDINAL_POSITION AS number, c.COLUMN_NAME AS name, c.ORDINAL_POSITION AS attnum, CASE c.IS_NULLABLE WHEN 'NO' THEN 't' ELSE 'f' END AS notnull, c.COLUMN_TYPE AS type, CASE WHEN c.COLUMN_KEY = 'PRI' THEN 't' ELSE 'f' END AS primarykey, CASE WHEN c.COLUMN_KEY = 'UNI' THEN 't' ELSE 'f' END AS uniquekey, k.REFERENCED_TABLE_NAME AS foreignkey, k.REFERENCED_COLUMN_NAME AS foreignkey_field, c.COLUMN_DEFAULT AS default_value FROM INFORMATION_SCHEMA.COLUMNS c LEFT JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE k ON c.TABLE_SCHEMA = k.TABLE_SCHEMA AND c.TABLE_NAME = k.TABLE_NAME AND c.COLUMN_NAME = k.COLUMN_NAME AND k.REFERENCED_TABLE_NAME IS NOT NULL WHERE c.TABLE_SCHEMA = 'database_name' AND c.TABLE_NAME = 'table_name' ORDER BY c.ORDINAL_POSITION;
```

**For PostgreSQL databases, run these commands:**
```sql
\c database_name
\d table_name
SELECT CASE relreplident WHEN 'd' THEN 'default' WHEN 'n' THEN 'nothing' WHEN 'f' THEN 'full' WHEN 'i' THEN 'index' END AS replica_identity FROM pg_class WHERE oid = 'table_name'::regclass;
SELECT * FROM information_schema.columns WHERE table_schema = 'schema_name' AND table_name = 'table_name' AND data_type in ('numeric', 'decimal');
SELECT f.attnum AS number, f.attname AS name, f.attnum, f.attnotnull AS notnull, pg_catalog.format_type(f.atttypid,f.atttypmod) AS type, CASE WHEN p.contype = 'p' THEN 't' ELSE 'f' END AS primarykey, CASE WHEN p.contype = 'u' THEN 't' ELSE 'f' END AS uniquekey, CASE WHEN p.contype = 'f' THEN g.relname END AS foreignkey, CASE WHEN p.contype = 'f' THEN p.confkey END AS foreignkey_fieldnum, CASE WHEN p.contype = 'f' THEN p.conkey END AS foreignkey_connnum, CASE WHEN f.atthasdef = 't' THEN pg_get_expr(d.adbin, d.adrelid) END AS default FROM pg_attribute f JOIN pg_class c ON c.oid = f.attrelid JOIN pg_type t ON t.oid = f.atttypid LEFT JOIN pg_attrdef d ON d.adrelid = c.oid AND d.adnum = f.attnum LEFT JOIN pg_namespace n ON n.oid = c.relnamespace LEFT JOIN pg_constraint p ON p.conrelid = c.oid AND f.attnum = ANY (p.conkey) LEFT JOIN pg_class AS g ON p.confrelid = g.oid WHERE c.relkind = 'r'::char AND n.nspname = 'schema_name' AND c.relname = 'table_name' AND f.attnum > 0 ORDER BY number;
```

**For PostgreSQL, check and fix replica identity if needed:**
If replica identity is not 'full', run:
```sql
ALTER TABLE table_name REPLICA IDENTITY FULL;
```

## Step 3: Update CSV Configuration

### 3.1 Locate Database CSV File
```bash
ls srp-table-database/
# Find your database CSV file (e.g., ars.csv)
```

### 3.2 Add Table Entry
Open `srp-table-database/database_name.csv` and add your table entry **in alphabetical order**:

```csv
Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,Nessie JAR,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
database_name,schema_name,table_name,TRUE,mysql,Prod-01,2,2,2,TRUE,180,primary_key_column,,partition_column,2.1.2,OWNER_ID,180,,lake_database_name,FALSE,1,,
```

**Important:** Use the same connector group numbers as existing tables in the same database.

## Step 4: Generate SRP Configurations
For every table generate the 
connectors/prod/postgresdb_kafka or mysqldb_kafka 
s3_sink and snapshot config 

Take reference from the existing configs and generate the new configs

PLEASE DONOT CREATE NEW TABLE GROUP VERSIONS ADD EACH CONFIG IN THE LATEST TABLE GROUP and the latest version of that. 
NEVER CREATE A NEW FILE FOR kafka and s3 sink. 

create a new file for snapshot config using existing references, if there are multiple tables add that to the same snapshot file. 
Create new s3_hudi files for each table using existing refs 


## Step 5: Update Connector Configurations

### 5.1 Update MySQL OR POSTGRES Source Connector

Find the latest table group configuration:
```bash
ls configurations/connectors/prod/mysqldb_kafka/ | grep "database_name.*table_group" | sort
```

Edit the latest version file (e.g., `mysql.database_name.schema.table_group_2.v5.conf`):

**Add your table to `table.include.list` (alphabetically):**
```json
"table.include.list": "schema.new_table_name,schema.existing_table1,schema.existing_table2"
```

**Add your table's primary key to `message.key.columns`:**
```json
"message.key.columns": "schema.new_table_name:primary_key;schema.existing_table1:id;schema.existing_table2:id"
```

### 5.2 Update S3 Sink Connector

Find the corresponding S3 sink configuration:
```bash
ls configurations/connectors/prod/s3_sink/ | grep "database_name.*table_group"
```

Edit the latest version file (e.g., `s3_sink.mysql.database_name.schema.table_group_2.v2.conf`):

**Add your table's topic to `topics` (alphabetically):**
```json
"topics": "mysql.database.schema.new_table_name,mysql.database.schema.existing_table1,mysql.database.schema.existing_table2"
```

## Step 6: Create Snapshot Connector

### 6.1 Create Snapshot Configuration File

**File name:** `configurations/connectors/prod/mysqldb_kafka/snapshots/mysql.database_name.schema.snapshot_YYYYMMDD.v1.conf`

**Content for MySQL:**
```json
{
  "config": {
    "heartbeat.interval.ms": "60000",
    "table.include.list": "schema.table_name",
    "message.key.columns": "schema.table_name:primary_key_column",
    "name": "mysql.database_name.schema.snapshot_YYYYMMDD.v1",
    "snapshot.mode": "initial_only",
    "snapshot.locking.mode": "none",
    "database.history.kafka.bootstrap.servers": "b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092",
    "connector.class": "io.debezium.connector.mysql.MySqlConnector",
    "database.server.name": "mysql.database_name",
    "database.history.kafka.topic": "db_history.mysql.database_name.table_group_X",
    "database.include.list": "database_name",
    "include.schema.changes": "true",
    "database.initial.statements": "SET SESSION max_execution_time=0",
    "producer.override.batch.size": "327680",
    "database.history.skip.unparseable.ddl": "true"
  },
  "name": "mysql.database_name.schema.snapshot_YYYYMMDD.v1"
}
```

**Content for PostgreSQL:**
```json
{
  "config": {
    "heartbeat.interval.ms": "60000",
    "table.include.list": "public.table_name",
    "message.key.columns": "public.table_name:primary_key_column",
    "name": "postgres.database_name.public.snapshot_YYYYMMDD.v1",
    "snapshot.mode": "initial_only",
    "database.server.name": "postgres.database_name",
    "connector.class": "io.debezium.connector.postgresql.PostgresConnector",
    "database.dbname": "database_name",
    "slot.drop.on.stop": "true",
    "slot.name": "postgres_database_name_public_snapshot_YYYYMMDD_v1",
    "plugin.name": "pgoutput",
    "provide.transaction.metadata": "true",
    "producer.override.batch.size": "327680",
    "slot.max.retries": "20"
  },
  "name": "postgres.database_name.public.snapshot_YYYYMMDD.v1"
}
```

## Step 7: Handle Special Cases

### 7.1 MySQL Tables with tinyint Columns

If your table has tinyint columns, add this to the MySQL source connector:
```json
{
  "config": {
    "converters": "boolean",
    "boolean.type": "io.debezium.connector.mysql.converters.TinyIntOneToBooleanConverter",
    "boolean.selector": "database_name.table_name.column_name_1,database_name.table_name.column_name_2"
  }
}
```

## Step 8: Verification

### 8.1 Check Generated Files
Verify these files exist:
- `configurations/nessie/prod/s3_hudi/database_name.table_name.v1.conf`
- `configurations/nessie/prod/hudi_snapshot_dump/database_name.table_name.v1.conf`

### 8.2 Verify Git Status
```bash
git status
```

Should show:
- **Modified:** CSV file and existing connector configs only
- **New files:** Only configs specific to your table

### 8.3 Validate JSON Syntax
```bash
python -m json.tool configurations/connectors/prod/mysqldb_kafka/mysql.database.schema.table_group_X.vY.conf
python -m json.tool configurations/connectors/prod/s3_sink/s3_sink.mysql.database.schema.table_group_X.vY.conf
python -m json.tool configurations/connectors/prod/mysqldb_kafka/snapshots/mysql.database.schema.snapshot_YYYYMMDD.v1.conf
```

## Step 9: Deployment Commands

**Note:** These commands require special permissions. User must run them: 
J

### 9.1 Deploy Updated Source Connector
```bash
tinker connect-add --connector_type mysqldb_kafka --config_file configurations/connectors/prod/mysqldb_kafka/mysql.database.schema.table_group_X.vY.conf --cluster_name prod-01 update
```

### 9.2 Deploy Updated Sink Connector
```bash
tinker connect-add --connector_type s3_sink --config_file configurations/connectors/prod/s3_sink/s3_sink.mysql.database.schema.table_group_X.vY.conf --cluster_name prod-01 update
```

### 9.3 Run Snapshot Connector
```bash
tinker connect-add --connector_type mysqldb_kafka --config_file configurations/connectors/prod/mysqldb_kafka/snapshots/mysql.database.schema.snapshot_YYYYMMDD.v1.conf --cluster_name prod-01 add
```

### 9.4 Monitor and Clean Up Snapshot
```bash
# Check status
tinker connect-cli --cluster_name=prod-01 list | grep snapshot

# After completion, delete snapshot connector
tinker connect-cli --name='mysql.database.schema.snapshot_YYYYMMDD.v1' --cluster_name=prod-01 delete
```

## File Naming Conventions

| File Type | Naming Pattern |
|-----------|----------------|
| Source Connector | `mysql.database.schema.table_group_X.vY.conf` |
| Sink Connector | `s3_sink.mysql.database.schema.table_group_X.vY.conf` |
| Snapshot Connector | `mysql.database.schema.snapshot_YYYYMMDD.v1.conf` |
| Nessie S3-Hudi | `database_name.table_name.v1.conf` |
| Nessie Snapshot | `database_name.table_name.v1.conf` |

## Critical Rules

1. **Always add tables alphabetically** in connector configurations
2. **Use existing table group numbers** - don't create new groups
3. **Follow exact primary key naming** from database
4. **Include partition columns** in Nessie configurations
5. **Only modify files related to your table**
6. **Use proper vault paths** for credentials
7. **Follow existing naming conventions exactly**

## Example: Complete `ars_job_run` Onboarding

### Table Details:
- Database: `ars` (MySQL)
- Table: `ars_job_run`
- Primary Key: `run_id`
- Partition: `created_at`
- Table Group: 2

### CSV Entry:
```csv
ars,ars,ars_job_run,TRUE,mysql,Prod-01,2,2,2,TRUE,180,run_id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_ars,FALSE,1,,
```

### Source Connector Updates:
```json
"table.include.list": "ars.ars_job_run,ars.backend_facility_transfer_attributes,..."
"message.key.columns": "ars.ars_job_run:run_id;ars.backend_facility_transfer_attributes:id;..."
```

### Sink Connector Updates:
```json
"topics": "mysql.ars.ars.ars_job_run,mysql.ars.ars.backend_facility_transfer_attributes,..."
```

### Snapshot File:
`mysql.ars.ars.snapshot_20250629.v1.conf`

## Troubleshooting

| Issue | Solution |
|-------|----------|
| Tinker commands fail | Check permissions and cluster connectivity |
| Configs rejected | Validate JSON syntax and required fields |
| Data doesn't flow | Verify topic names match between source and sink |
| Snapshot fails | Check table permissions and primary key configuration |
| Vault authentication fails | Verify VAULT_AUTH_GITHUB_TOKEN is set correctly |

## Final Checklist

- [ ] Database credentials retrieved successfully
- [ ] Table structure verified (including replica identity for PostgreSQL)
- [ ] CSV entry added in alphabetical order
- [ ] Source connector updated with table and primary key
- [ ] Sink connector updated with topic
- [ ] Snapshot connector created with correct naming
- [ ] Special cases handled (tinyint columns, etc.)
- [ ] All JSON files validated
- [ ] Git status shows only relevant file changes
- [ ] Deployment commands prepared for user execution

This guide ensures consistent and reliable table onboarding into the SRP system.









###################################################### AIRFLOW DE DAGS ############################################

In the airflow-de-dags  repo, take reference from existing replication dag configs and onboard the below tables. 
Use existing references and configs and strictly adhere to them. Don't generate dag files just generate config files. 
Add all the tables to existing config files