Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
cart,public,additional_charges_config,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_cart,FALSE,1,,
cart,public,cart_additional_charge,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_cart,FALSE,1,,
cart,public,free_delivery_journal,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_cart,FALSE,1,,
cart,public,gr_cart_cashback_mapping,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_cart,FALSE,1,,
cart,public,gr_cart_promo_action_mapping,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_cart,FALSE,1,,
cart,public,subscribed_skus,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_cart,FALSE,1,,
cart,public,gr_cart_promo,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_cart,FALSE,1,,
cart,public,gr_cart_milestone_mapping,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_cart,FALSE,1,,
cart,public,gr_cart_offer,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_cart,FALSE,1,,
