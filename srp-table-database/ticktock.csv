Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
ticktock,public,cart_segments,FALSE,postgres,Prod-01,1,1,1,TRUE,120,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_ticktock,FALSE,1,,
ticktock,public,merchant_slot_cashback,FALSE,postgres,Prod-01,1,1,1,TRUE,120,id,,,2.1.2,S089D0GQRCJ,180,,lake_ticktock,FALSE,1,,
ticktock,public,merchant_slot_charge,FALSE,postgres,Prod-01,1,1,1,TRUE,120,id,,,2.1.2,S089D0GQRCJ,180,,lake_ticktock,FALSE,1,,
