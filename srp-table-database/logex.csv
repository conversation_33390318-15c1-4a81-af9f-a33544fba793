Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
logex,public,activity,FALSE,postgres,Prod-01,1,1,1,TRUE,120,id,,,2.1.2,S089D0GQRCJ,180,5,,FALSE,1,,
logex,public,activity_event_log,FALSE,postgres,Prod-01,1,1,1,TRUE,120,id,,,2.1.2,S089D0GQRCJ,180,5,,FALSE,1,,
logex,public,batch,FALSE,postgres,Prod-01,1,1,1,TRUE,120,id,,,2.1.2,S089D0GQRCJ,180,5,,FALSE,1,,
logex,public,container_activity,FALSE,postgres,Prod-01,1,1,1,TRUE,120,id,,created_date,2.1.2,S089D0GQRCJ,180,5,,FALSE,1,,
logex,public,location_db,FALSE,postgres,Prod-01,1,1,1,TRUE,120,id,,,2.1.2,S089D0GQRCJ,180,5,,FALSE,1,,
logex,public,logistic_container,FALSE,postgres,Prod-01,1,1,1,TRUE,120,id,,,2.1.2,S089D0GQRCJ,180,5,,FALSE,1,,
logex,public,logistic_order,FALSE,postgres,Prod-01,1,1,1,TRUE,120,id,,,2.1.2,S089D0GQRCJ,180,5,,FALSE,1,,
logex,public,sub_order,FALSE,postgres,Prod-01,1,1,1,TRUE,120,id,,created_date,2.1.2,S089D0GQRCJ,180,5,,FALSE,1,,
logex,public,container_count,FALSE,postgres,Prod-01,1,1,1,TRUE,120,id,,,2.1.2,S089D0GQRCJ,180,5,,FALSE,1,,
