Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
adserver_analytics,public,ba_category_insights_metrics,TRUE,postgres,Prod-01,1,1,1,TRUE,180,"category_id,metric,city_name,metric_type",,start_date,2.1.2,S089D0GQRCJ,180,,lake_adserver_analytics,FALSE,1,,
adserver_analytics,public,ba_sales_metrics,TRUE,postgres,Prod-01,1,1,1,TRUE,180,"category_id,metric,city_name,brand_id",,start_date,2.1.2,S089D0GQRCJ,180,,lake_adserver_analytics,FALSE,1,,
adserver_analytics,public,ba_market_basket_insights,TRUE,postgres,Prod-01,1,1,1,TRUE,180,"affinity_category_id,category_id,metric,city_name,brand_id",,start_date,2.1.2,S089D0GQRCJ,180,,lake_adserver_analytics,FALSE,1,,
adserver_analytics,public,ba_search_insights_metrics,TRUE,postgres,Prod-01,1,1,1,TRUE,180,"most_bought_product_id,keyword,category_id",,start_date,2.1.2,S089D0GQRCJ,180,,lake_adserver_analytics,FALSE,1,,
adserver_analytics,public,ba_user_insight_metric,TRUE,postgres,Prod-01,1,1,1,TRUE,180,"brand_id,category_id,metric,city_name",,start_date,2.1.2,S089D0GQRCJ,180,,lake_adserver_analytics,FALSE,1,,
adserver_analytics,public,ba_awareness_and_search_metrics,TRUE,postgres,Prod-01,1,1,1,TRUE,180,"category_id,brand_id,city_name,metric",,start_date,2.1.2,S089D0GQRCJ,180,,lake_adserver_analytics,FALSE,1,,
adserver_analytics,public,ba_brand_config,TRUE,postgres,Prod-01,1,1,1,TRUE,180,"category_id,brand_id,config_type",,start_date,2.1.2,S089D0GQRCJ,180,,lake_adserver_analytics,FALSE,1,,
adserver_analytics,public,ba_brand_keyword_metrics,TRUE,postgres,Prod-01,1,1,1,TRUE,180,"brand_id,keyword,metric,city_name",,start_date,2.1.2,S089D0GQRCJ,180,,lake_adserver_analytics,FALSE,1,,
