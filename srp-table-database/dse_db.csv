Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
dse_db,public,complementary_products,TRUE,postgres,Prod-01,1,1,1,TRUE,180,"antecedent_pid,consequent_ptype",,,2.1.2,S089D0GQRCJ,180,,lake_dse_db,FALSE,1,,
dse_db,public,because_you_bought_usecase_v2,TRUE,postgres,Prod-01,1,1,1,TRUE,180,collection_uuid,,,2.1.2,S089D0GQRCJ,180,,lake_dse_db,FALSE,1,,
dse_db,public,frequently_bought_groups_meta,TRUE,postgres,Prod-01,1,1,1,TRUE,180,"group_id,is_enabled",,,2.1.2,S089D0GQRCJ,180,,lake_dse_db,FALSE,1,,
dse_db,public,trending_entities,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_dse_db,FALSE,1,,
dse_db,public,story_information,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_dse_db,FALSE,1,,
dse_db,public,personalisation_story_metadata,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_dse_db,FALSE,1,,
dse_db,public,master_reranking_data_v2,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_dse_db,FALSE,1,,
