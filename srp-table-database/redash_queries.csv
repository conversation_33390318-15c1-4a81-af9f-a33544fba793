Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
redash_queries,public,dashboards,TRUE,postgres,Prod-01,1,1,2,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_redash_queries,FALSE,1,,
redash_queries,public,widgets,TRUE,postgres,Prod-01,1,1,2,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_redash_queries,FALSE,1,,
redash_queries,public,data_sources,TRUE,postgres,Prod-01,1,1,2,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_redash_queries,FALSE,1,,
redash_queries,public,groups,TRUE,postgres,Prod-01,1,1,2,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_redash_queries,FALSE,1,,
redash_queries,public,data_source_groups,TRUE,postgres,Prod-01,1,1,2,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_redash_queries,FALSE,1,,
redash_queries,public,destinations,TRUE,postgres,Prod-01,1,1,2,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_redash_queries,FALSE,1,,
redash_queries,public,destination_sync_history,FALSE,postgres,Prod-01,2,2,1,TRUE,180,id,,synced_at,2.1.2,S089D0GQRCJ,180,,lake_redash_queries,FALSE,1,,
redash_queries,public,queries,TRUE,postgres,Prod-01,2,2,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_redash_queries,FALSE,1,,
redash_queries,public,users,TRUE,postgres,Prod-01,1,1,2,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_redash_queries,FALSE,1,,
redash_queries,public,visualizations,TRUE,postgres,Prod-01,1,1,2,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_redash_queries,FALSE,1,,
redash_queries,public,query_results,FALSE,postgres,Prod-01,2,2,1,TRUE,180,id,,retrieved_at,2.1.2,S089D0GQRCJ,180,,lake_redash_queries,FALSE,1,,
