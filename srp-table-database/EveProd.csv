Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
EveProd,dbo,UserAttendance,TRUE,sqlserver,Prod-01,1,1,1,TRUE,180,"DeviceID,UserID,AttDateTime",,UpdateedOn,2.1.2,S089D0GQRCJ,180,,lake_eveprod,FALSE,1,,
Eve<PERSON>rod,dbo,tblemployee,TRUE,sqlserver,Prod-01,1,1,1,TRUE,180,"PRESENTCARDNO,SSN",,,2.1.2,S089D0GQRCJ,180,,lake_eveprod,FALS<PERSON>,1,,
<PERSON><PERSON><PERSON>,dbo,tbltimeregister,TR<PERSON>,sqlserver,Prod-01,1,1,1,TRUE,180,"DateOFFICE,SSN",,DateO<PERSON>IC<PERSON>,2.1.2,S089D0GQRCJ,180,,lake_eveprod,FALSE,1,,
<PERSON><PERSON>rod,dbo,tblOutWorkRegister,TRUE,sqlserver,Prod-01,1,1,1,TRUE,180,"PAYCODE,DateOFFICE,SSN",,DateOFFICE,2.1.2,S089D0GQRCJ,180,,lake_eveprod,FALSE,1,,
EveProd,dbo,tblEmployeeShiftMaster,TRUE,sqlserver,Prod-01,1,1,1,TRUE,180,"CARDNO,SSN",,,2.1.2,S089D0GQRCJ,180,,lake_eveprod,FALSE,1,,
EveProd,dbo,tbluser,TRUE,sqlserver,Prod-01,1,1,1,TRUE,180,SSN,,,2.1.2,S089D0GQRCJ,180,,lake_eveprod,FALSE,1,,
EveProd,dbo,Userdetail,TRUE,sqlserver,Prod-01,1,1,1,TRUE,180,"DeviceID,UserID",,,2.1.2,S089D0GQRCJ,180,,lake_eveprod,FALSE,1,,
EveProd,dbo,TblCityMaster,TRUE,sqlserver,Prod-01,1,1,1,TRUE,180,City_Code,,,2.1.2,S089D0GQRCJ,180,,lake_eveprod,FALSE,1,,
EveProd,dbo,tblDepartment,TRUE,sqlserver,Prod-01,1,1,1,TRUE,180,"DepartmentCode,CompanyCode",,,2.1.2,S089D0GQRCJ,180,,lake_eveprod,FALSE,1,,
EveProd,dbo,tblShiftMaster,TRUE,sqlserver,Prod-01,1,1,1,TRUE,180,"SHIFT,CompanyCode",,,2.1.2,S089D0GQRCJ,180,,lake_eveprod,FALSE,1,,
EveProd,dbo,tblLocation,TRUE,sqlserver,Prod-01,1,1,1,TRUE,180,"LocationCode,COMPANYCODE",,,2.1.2,S089D0GQRCJ,180,,lake_eveprod,FALSE,1,,
