Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
kitchen,public,dish,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,ingredient,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,item_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,orders,TRUE,postgres,Prod-01,1,1,2,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,product_transformation,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,store_station,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,sub_order,TRUE,postgres,Prod-01,1,1,2,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,sub_order_item,TRUE,postgres,Prod-01,1,1,2,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,subdish,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,packaging_bags,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,activity_logs,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,complaints_rca,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,preparation_task_log,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,station,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,order_states,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,product,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,store_product_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,foodware_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,product_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,station_product_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,entity,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,entity_schedule,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,user_entity_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,shift_record,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,attendance_summary,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,roster_planner,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
kitchen,public,return_order,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_kitchen,FALSE,1,,
