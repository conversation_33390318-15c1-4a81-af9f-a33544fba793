Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
emergency_services,public,ambulance_checklist_session,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_ts,2.1.2,S089D0GQRCJ,180,,lake_emergency_services,FALSE,1,,
emergency_services,public,ambulance_checklist_item_update,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_ts,2.1.2,S089D0GQRCJ,180,,lake_emergency_services,FALSE,1,,
emergency_services,public,shift,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_emergency_services,FALSE,1,,
emergency_services,public,patient_details,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_emergency_services,FALSE,1,,
emergency_services,public,medical_staff,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_emergency_services,FALSE,1,,
emergency_services,public,ambulance,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_emergency_services,FALSE,1,,
emergency_services,public,ambulance_trip_staff,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_ts,2.1.2,S089D0GQRCJ,180,,lake_emergency_services,FALSE,1,,
emergency_services,public,depot,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_emergency_services,FALSE,1,,
emergency_services,public,ambulance_trip,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_ts,2.1.2,S089D0GQRCJ,180,,lake_emergency_services,FALSE,1,,
emergency_services,public,ambulance_trip_event,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_ts,2.1.2,S089D0GQRCJ,180,,lake_emergency_services,FALSE,1,,
emergency_services,public,ambulance_order_trip_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_ts,2.1.2,S089D0GQRCJ,180,,lake_emergency_services,FALSE,1,,
emergency_services,public,consumable,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_emergency_services,FALSE,1,,
emergency_services,public,medical_remark,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_ts,2.1.2,S089D0GQRCJ,180,,lake_emergency_services,FALSE,1,,
emergency_services,public,ambulance_staff_evaluation,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_ts,2.1.2,S089D0GQRCJ,180,,lake_emergency_services,FALSE,1,,
emergency_services,public,consumable_batch,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_ts,2.1.2,S089D0GQRCJ,180,,lake_emergency_services,FALSE,1,,
