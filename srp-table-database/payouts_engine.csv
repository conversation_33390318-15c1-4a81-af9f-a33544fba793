Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
payouts_engine,public,payouts_event_entries,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,event_ts,2.1.2,S089D0GQRCJ,180,,lake_payouts_engine,FALSE,1,,
payouts_engine,public,payouts_outbound_event_entries,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,event_ts,2.1.2,S089D0GQRCJ,180,,lake_payouts_engine,FALSE,1,,
payouts_engine,public,payouts_event_pivots,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_payouts_engine,FALSE,1,,
payouts_engine,public,payouts_picker_daily_pivots,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_payouts_engine,FALSE,1,,
payouts_engine,public,payouts_picker_inbound_event,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_payouts_engine,FALSE,1,,
