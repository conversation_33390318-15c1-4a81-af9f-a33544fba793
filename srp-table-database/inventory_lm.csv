Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
inventory_lm,public,layout_component,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_inventory_lm,FALSE,1,,
inventory_lm,public,legend_mapping_detail,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_inventory_lm,FALSE,1,,
inventory_lm,public,layout_change_request_log,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_inventory_lm,FALSE,1,,
inventory_lm,public,outlet_layout,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_inventory_lm,FALSE,1,,
inventory_lm,public,outlet_floor_path_version,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_inventory_lm,FALSE,1,,
inventory_lm,public,outlet_floor_shortest_path,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,version_id,2.1.2,S089D0GQRCJ,180,,lake_inventory_lm,FALSE,1,,
