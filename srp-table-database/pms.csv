Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
pms,public,printings,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_pms,FALSE,1,,
pms,public,printing_events,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_pms,FALSE,1,,
pms,public,printing_serviceability,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_pms,FALSE,1,,
pms,public,paas_ticketing_system,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_pms,FALSE,1,,
