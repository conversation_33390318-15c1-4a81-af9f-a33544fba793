Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
supply_orchestrator,public,sco_city,FALSE,postgres,Prod-01,2,2,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_dispatch_point,FALSE,postgres,Prod-01,2,2,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_edge,FALSE,postgres,Prod-01,1,1,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_merchant_capacity_throttle_log,FALSE,postgres,Prod-01,2,2,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_merchant_capacity,FALSE,postgres,Prod-01,1,1,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_merchant_order_event_log,FALSE,postgres,Prod-01,1,1,1,TRUE,240,id,,install_ts,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_merchant_slot_rollout_config,FALSE,postgres,Prod-01,1,1,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_merchant_slot,FALSE,postgres,Prod-01,1,1,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_merchant_week_schedule_refresh_log,FALSE,postgres,Prod-01,1,1,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_merchant,FALSE,postgres,Prod-01,2,2,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_network_path,FALSE,postgres,Prod-01,2,2,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_node,FALSE,postgres,Prod-01,1,1,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_order,FALSE,postgres,Prod-01,1,1,1,TRUE,240,id,,install_ts,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_path_capacity,FALSE,postgres,Prod-01,2,2,2,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_scheduled_capacity,FALSE,postgres,Prod-01,1,1,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_scheduled_network_mapping,FALSE,postgres,Prod-01,1,1,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_sheet_file,FALSE,postgres,Prod-01,1,1,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_slot,FALSE,postgres,Prod-01,2,2,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_station_capacity_throttle_log,FALSE,postgres,Prod-01,1,1,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_station_capacity,FALSE,postgres,Prod-01,2,2,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_station_order_event_log,FALSE,postgres,Prod-01,2,2,1,TRUE,240,id,,install_ts,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_station,FALSE,postgres,Prod-01,2,2,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_suborder,FALSE,postgres,Prod-01,2,2,1,TRUE,240,id,,install_ts,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_warehouse_capacity_throttle_log,FALSE,postgres,Prod-01,2,2,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_warehouse_capacity,FALSE,postgres,Prod-01,2,2,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_warehouse_suborder_event_log,FALSE,postgres,Prod-01,1,1,1,TRUE,240,id,,install_ts,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
supply_orchestrator,public,sco_warehouse,FALSE,postgres,Prod-01,1,1,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
