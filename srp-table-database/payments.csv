Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
payments,public,add_money,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_payments,FALSE,1,,
payments,public,bank,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_payments,FALSE,1,,
payments,public,collectable_amount_order_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_payments,FALSE,1,,
payments,public,external_transaction,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,create_ts,2.1.2,S089D0GQRCJ,180,,lake_payments,FALSE,1,,
payments,public,mandate_info,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_payments,FALSE,1,,
payments,public,order_issue_cashback_mapping,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_payments,FALSE,1,,
payments,public,payment_offer,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_payments,FALSE,1,,
payments,public,payment_type,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_payments,FALSE,1,,
payments,public,payu_settlement,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_payments,FALSE,1,,
payments,public,pre_auth_wallet,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_payments,FALSE,1,,
payments,public,refund_item_mapping,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_payments,FALSE,1,,
payments,public,user_payment_profile,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_payments,FALSE,1,,
payments,public,payment,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_payments,FALSE,1,,
