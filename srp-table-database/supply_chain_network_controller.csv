Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
supply_chain_network_controller,public,merchant,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_supply_chain_network_controller,FALSE,1,,
supply_chain_network_controller,public,merchant_polygon,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_supply_chain_network_controller,FALSE,1,,
supply_chain_network_controller,public,change_request,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_supply_chain_network_controller,FALSE,1,,
supply_chain_network_controller,public,change_request_polygon_change_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_supply_chain_network_controller,FALSE,1,,
supply_chain_network_controller,public,polygon_change,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_supply_chain_network_controller,FALSE,1,,
