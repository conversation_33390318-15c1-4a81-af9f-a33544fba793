Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
anomalies,anomalies,b2b_invoice_payload,FALSE,mysql,Prod-01,1,1,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,lake_anomalies,FALSE,1,,
anomalies,anomalies,b2b_invoice_product_tax_details,FALSE,mysql,Prod-01,1,1,1,TRUE,240,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_anomalies,FALSE,1,,
anomalies,anomalies,b2b_invoice_run,FALSE,mysql,Prod-01,1,1,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,lake_anomalies,FALSE,1,,
anomalies,anomalies,b2b_invoice_sequence,FALSE,mysql,Prod-01,1,1,1,TRUE,240,id,,,2.1.2,S089D0GQRCJ,180,,lake_anomalies,FALSE,1,,
anomalies,anomalies,b2b_invoice_variant_details,FALSE,mysql,Prod-01,1,1,1,TRUE,240,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_anomalies,FALSE,1,,
anomalies,anomalies,b2b_invoice_variant_sales,FALSE,mysql,Prod-01,1,1,1,TRUE,240,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_anomalies,FALSE,1,,
anomalies,anomalies,b2b_invoice_vendor_sales_margin,FALSE,mysql,Prod-01,1,1,1,TRUE,240,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_anomalies,FALSE,1,,
