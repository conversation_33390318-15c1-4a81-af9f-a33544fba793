Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
logistics_server,public,logistics_address,TRUE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_allocation_batch,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_allocation_request,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,2,,
logistics_server,public,logistics_dark_store_projection,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_express_allocation_field_executive_queue,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_node_address,TRUE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_node,TRUE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_notification_client,TRUE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_shipment_item_complaint,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_shipment_item,TRUE,postgres,Prod-01,1,1,1,TRUE,60,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_shipment_label_metadata,TRUE,postgres,Prod-01,1,1,1,TRUE,60,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,2,,
logistics_server,public,logistics_shipment_label,TRUE,postgres,Prod-01,1,1,1,TRUE,60,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,2,,
logistics_server,public,logistics_shipment,TRUE,postgres,Prod-01,1,1,1,TRUE,60,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,2,,
logistics_server,public,logistics_task_call_detail,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_task_file,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_task,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_user_profile_node,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_user_profile,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_user_roster,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_user,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_workflow_type,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_workflow,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,shipments_shipmentstatelog,TRUE,postgres,Prod-01,1,1,1,TRUE,60,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,2,,
logistics_server,public,task_management_taskstatelog,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_user_import,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_payouts_daily_projection,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,date,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_user_daily_activity_projection,FALSE,postgres,Prod-01,2,2,1,TRUE,60,id,activity_date,activity_date,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_disputes,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_dispute_types,FALSE,postgres,Prod-01,1,1,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_order_3pl_event_log,TRUE,postgres,Prod-01,2,2,1,TRUE,60,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_feature,TRUE,postgres,Prod-01,2,2,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_feature_node,TRUE,postgres,Prod-01,2,2,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
logistics_server,public,logistics_drop_zone,FALSE,postgres,Prod-01,2,2,1,TRUE,60,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_logistics,FALSE,1,,
