Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
finance_db,public,auth_user,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_finance,FALSE,1,,
finance_db,public,fe_cash_transaction,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_finance,FALSE,1,,
finance_db,public,order_history,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_finance,FALSE,1,,
finance_db,public,order_procurement,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_finance,FALSE,1,,
finance_db,public,order_reconciliation,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_finance,FALSE,1,,
finance_db,public,pinelabs_transaction,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_finance,FALSE,1,,
finance_db,public,station_cash_declaration,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_finance,FALSE,1,,
finance_db,public,station_cash_transaction,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_finance,FALSE,1,,
