Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
storeops,public,packaging_suggestion_details,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,activity,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,er,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,erline,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,er_container,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,item_activity,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_date,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,2,,
storeops,public,location_inventory,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,item_details,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,item_exceptions,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,business_eventdb,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,created_date,created_date,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,poindent,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,poline,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,user_details,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,item_site_tag_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,created_at,,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,inventory_update_log,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,created_date,created_date,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,order_complaint,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,discrepancy_logs,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,created_date,created_date,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,location,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,site_id,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,dbasync_task,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,execution_min_time,execution_min_time,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,er_container_item,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_date,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,product_info,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,inward_stock_attribute,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,product_exception,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,asset_tracking,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,asset_tracking_log,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_date,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,site,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
storeops,public,consignment_metrics,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_date,2.1.2,S089D0GQRCJ,180,,lake_storeops,FALSE,1,,
