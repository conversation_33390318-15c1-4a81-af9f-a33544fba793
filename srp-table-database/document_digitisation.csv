Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
document_digitisation,public,digitisation_request,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,digitisation_request_action,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,digitised_document,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,digitised_document_log,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,digitisation_invoice_mapping_v2,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,verified_invoice_data_log,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,verified_invoice_data,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,verification_request,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,invoice_verification_request,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,purchase_order_data,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,purchase_item_data,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,grn_data,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,digitised_invoice_data,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,digitised_invoice_item_data,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,discrepancy_note_data,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,discrepancy_note_data_dump,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,seller,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,tenant,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,user_management_user_group,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,user_seller_map,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,user_tenant_map,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,verification_request_step,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,verification_step,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,verification_trail,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,auth_user,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,verified_invoice_items_data,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,verified_invoice_items_data_log,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,digitise_bulk_invoice_request,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
document_digitisation,public,digitise_invoice_request,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_document_digitisation,FALSE,1,,
