Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
locus_v2,public,form,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_locus_v2,FALSE,1,,
locus_v2,public,user_zones,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_locus_v2,FALSE,1,,
locus_v2,public,user_data,FALSE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_locus_v2,FALSE,1,,
locus_v2,public,user_data,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_locus_v2,FALSE,2,"phone,email,device_token",
locus_v2,public,project,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_locus_v2,FALSE,1,,
locus_v2,public,status_type,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_locus_v2,FALSE,1,,
locus_v2,public,form_team_status_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_locus_v2,FALSE,1,,
locus_v2,public,network_team,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_locus_v2,FALSE,1,,
locus_v2,public,team_final_status,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_locus_v2,FALSE,1,,
locus_v2,public,form_final_status,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_locus_v2,FALSE,1,,
locus_v2,public,user_ops_form_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,"user_id,form_id",,,2.1.2,S089D0GQRCJ,180,,lake_locus_v2,FALSE,1,,
locus_v2,public,user_re_project_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,"project_id,user_id",,,2.1.2,S089D0GQRCJ,180,,lake_locus_v2,FALSE,1,,
locus_v2,public,user_roles,TRUE,postgres,Prod-01,1,1,1,TRUE,180,,,,2.1.2,S089D0GQRCJ,180,,lake_locus_v2,FALSE,1,,
locus_v2,public,user_role_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,"user_id,role_id",,,2.1.2,S089D0GQRCJ,180,,lake_locus_v2,FALSE,1,,
locus_v2,public,user_zone_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,"user_id,zone_id",,,2.1.2,S089D0GQRCJ,180,,lake_locus_v2,FALSE,1,,
locus_v2,public,document_classifications,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_locus_v2,FALSE,1,,
locus_v2,public,document_form_mappings,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_locus_v2,FALSE,1,,
locus_v2,public,document_records,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_locus_v2,FALSE,1,,
locus_v2,public,form_final_status_lookup,TRUE,postgres,Prod-01,1,1,1,TRUE,180,"form_id,team_id,status_id",,created_at,2.1.2,S089D0GQRCJ,180,,lake_locus_v2,FALSE,1,,
locus_v2,public,project_final_status_lookup,TRUE,postgres,Prod-01,1,1,1,TRUE,180,form_id,,,2.1.2,S089D0GQRCJ,180,,lake_locus_v2,FALSE,1,,
locus_v2,public,sub_forms,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_locus_v2,FALSE,1,,
