Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
adserver,public,ad_manager,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,ad_user,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,advertiser,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,advertiser_asset_mapping,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,auction,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,bid,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,keyword,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,stats,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,sponsored_asset,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,search_sib_bidding_asset,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,search_product_bidding_asset,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,bidding_asset,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,search_brand_strip_bidding_asset,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,rules,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,winner,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,category,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,category_keyword_mapping,FALSE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,campaign_metrics,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,bill_details,TRUE,postgres,Prod-01,1,1,1,TRUE,360,campaign_id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,campaign,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,rules_keyword,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,sub_campaign,TRUE,postgres,Prod-01,1,1,1,TRUE,360,sub_campaign_id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,targeting_rule,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,campaign_daily_data,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,brand_widget_page,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,brand,TRUE,postgres,Prod-01,1,1,1,TRUE,360,"id,advertiser_id",,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,campaign_daily_budget_metrics,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,created_ts,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,plan,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,campaign_plan_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,audit_log,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,created_ts,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,keyword_ptype_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,181,,lake_adserver,FALSE,1,,
adserver,public,cost_rule,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,181,,lake_adserver,FALSE,1,,
adserver,public,keyword_attributes,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,created_ts,2.1.2,S089D0GQRCJ,181,,lake_adserver,FALSE,1,,
adserver,public,product,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,181,,lake_adserver,FALSE,1,,
adserver,public,scheduled_changes,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,created_ts,2.1.2,S089D0GQRCJ,181,,lake_adserver,FALSE,1,,
adserver,public,city,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,brand_store,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,created_ts,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,campaign_brand_store_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,ticket,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,created_ts,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,ticket_comments,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,created_ts,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,feature_rollout,TRUE,postgres,Prod-01,2,2,2,TRUE,360,id,,created_ts,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,advertiser_transactions,TRUE,postgres,Prod-01,2,2,2,TRUE,360,id,,created_ts,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,ba_membership,TRUE,postgres,Prod-01,2,2,2,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,story,TRUE,postgres,Prod-01,2,2,2,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,campaign_history,TRUE,postgres,Prod-01,2,2,2,TRUE,360,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,slot_projection,TRUE,postgres,Prod-01,2,2,2,TRUE,360,id,,created_ts,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
adserver,public,slot_booking,TRUE,postgres,Prod-01,2,2,2,TRUE,360,id,,created_ts,2.1.2,S089D0GQRCJ,180,,lake_adserver,FALSE,1,,
