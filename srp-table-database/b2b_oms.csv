Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
b2b_oms,public,b2b_order,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_b2b_oms,FALSE,1,,
b2b_oms,public,b2b_order_item,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_b2b_oms,FALSE,1,,
b2b_oms,public,b2b_order_transaction_entity,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_b2b_oms,FALSE,1,,
b2b_oms,public,b2b_order_status_log,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_b2b_oms,FALSE,1,,
b2b_oms,public,sto,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_b2b_oms,FALSE,1,,
b2b_oms,public,sto_item_procurement_detail,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_b2b_oms,FALSE,1,,
b2b_oms,public,container,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_b2b_oms,FALSE,1,,
b2b_oms,public,container_transaction_entity,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_b2b_oms,FALSE,1,,
b2b_oms,public,container_status_log,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_b2b_oms,FALSE,1,,
b2b_oms,public,b2b_order_item_activity_log,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_b2b_oms,FALSE,1,,
b2b_oms,public,container_order_item_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_b2b_oms,FALSE,1,,
b2b_oms,public,invoice,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_b2b_oms,FALSE,1,,
b2b_oms,public,invoice_item,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_b2b_oms,FALSE,1,,
b2b_oms,public,consignment_item,TRUE,postgres,Prod-01,2,2,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_b2b_oms,FALSE,1,,
b2b_oms,public,consignment_invoice_mapping,TRUE,postgres,Prod-01,2,2,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_b2b_oms,FALSE,1,,
b2b_oms,public,consignment_transaction_entity,TRUE,postgres,Prod-01,2,2,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_b2b_oms,FALSE,1,,
b2b_oms,public,consignment_status_log,TRUE,postgres,Prod-01,2,2,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_b2b_oms,FALSE,1,,
b2b_oms,public,consignment_container_status_log,TRUE,postgres,Prod-01,2,2,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_b2b_oms,FALSE,1,,
b2b_oms,public,consignment_container_mapping,TRUE,postgres,Prod-01,2,2,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_b2b_oms,FALSE,1,,
b2b_oms,public,consignment,TRUE,postgres,Prod-01,2,2,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_b2b_oms,FALSE,1,,
