Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
airflow,public,dag,TRUE,postgres,Prod-01,1,1,1,TRUE,180,dag_id,,,2.1.2,S089D0GQRCJ,180,,lake_airflow,FALSE,1,,
airflow,public,dag_run,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,execution_date,2.1.2,S089D0GQRCJ,180,,lake_airflow,FALSE,1,,
airflow,public,slot_pool,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_airflow,FALSE,1,,
airflow,public,task_instance,TRUE,postgres,Prod-01,1,1,1,TRUE,180,"task_id,dag_id,run_id,map_index",,start_date,2.1.2,S089D0GQRCJ,180,,lake_airflow,FALSE,1,,
airflow,public,serialized_dags,TRUE,postgres,Prod-01,1,1,1,TRUE,180,dag_id,,last_updated,2.1.2,S089D0GQRCJ,180,,lake_airflow,FALSE,1,,
