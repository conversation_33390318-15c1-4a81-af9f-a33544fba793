Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
asset_tracking,public,code_product_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,code,,,2.1.2,S089D0GQRCJ,180,,lake_asset_tracking,FALSE,1,,
asset_tracking,public,tenant,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_asset_tracking,FALSE,1,,
asset_tracking,public,code_label_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,code,,,2.1.2,S089D0GQRCJ,180,,lake_asset_tracking,FALSE,1,,
asset_tracking,public,old_code_label_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_asset_tracking,FALSE,1,,
