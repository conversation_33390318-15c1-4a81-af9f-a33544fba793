Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
vendor_console,public,fleet_trip_details,FALSE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,fleet_consignment,FALSE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,fleet_user_details,FALSE,postgres,Prod-01,1,1,1,TRUE,180,phone_number,,,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,fleet_trip_state_transition_log,FALSE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,fleet_consignment_state_transition_log,FALSE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,fleet_consignment_invoice_mapping,FALSE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,fleet_trip_location,FALSE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,fleet_trip_location_log,FALSE,postgres,Prod-01,1,1,1,TRUE,180,id,created_at,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,user_details,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,item_proxy_category,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,invoice,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,invoice_event_log,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,invoice_payment_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,payment_date,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,invoice_payment_details,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,client_po_details,TRUE,postgres,Prod-01,1,1,1,TRUE,180,po_number,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,client_po_items,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,appointment,TRUE,postgres,Prod-01,1,1,1,TRUE,180,appointment_id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,appointment_capacity_config,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,appointment_capacity_config_log,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,appointment_facility_day_capacity,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,appointment_facility_slot,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,appointment_facility_slot_log,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,appointment_log,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,appointment_po_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,appointment_slot_config,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,appointment_slot_config_log,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,report_requests,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,cms_assortment_request,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,cms_assortment_request_log,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,facility_day_level_capacity,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,appointment_slot_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,slot_capacity,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,facility_slot_time,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,facility_slot_time_log,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,slot_capacity_log,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,facility_day_level_capacity_log,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,facility_config,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,facility_config_log,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,base_bulk_upload_request_tracker,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,day_capacity_request_data_tracker,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,slot_capacity_request_data_tracker,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,appointment_slot_mapping_log,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,courier_partner_days_config,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,courier_partner_details,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,po_reservation,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,po_reservation_log,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,reservation_slot_details,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,reservation_slot_details_log,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,vendor_facility_auto_release,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,slot_capacity_config,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,entity,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,user_entity_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,entity_plan_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,auth_plan,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
vendor_console,public,shipment,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_vendor_console,FALSE,1,,
