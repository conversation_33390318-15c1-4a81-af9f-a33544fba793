Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
oms_bifrost,public,oms_abuse_item,FALSE,postgres,Prod-01,1,1,2,TRUE,120,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_abuse_user,FALSE,postgres,Prod-01,1,1,2,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_cart_address,TRUE,postgres,Prod-01,2,2,2,TRUE,180,id,install_ts,install_ts,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_cart,TRUE,postgres,Prod-01,2,2,1,TRUE,60,id,install_ts,install_ts,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_comment,TRUE,postgres,Prod-01,1,1,2,TRUE,180,id,,install_ts,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_config_variable,FALSE,postgres,Prod-01,1,1,3,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_event_type,TRUE,postgres,Prod-01,1,1,1,TRUE,360,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_freebie_log,FALSE,postgres,Prod-01,1,1,2,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_freebie,FALSE,postgres,Prod-01,1,1,2,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_grievance_config_variable,FALSE,postgres,Prod-01,1,1,2,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_internal_order_mapping,TRUE,postgres,Prod-01,2,2,2,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_issue_item,FALSE,postgres,Prod-01,1,1,2,TRUE,180,id,install_ts,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_issue_type,FALSE,postgres,Prod-01,2,2,2,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_issue,FALSE,postgres,Prod-01,1,1,2,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_merchant,TRUE,postgres,Prod-01,1,1,1,TRUE,60,id,install_ts,install_ts,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_order_cashback,FALSE,postgres,Prod-01,1,1,2,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_order_delayed_reschedule,FALSE,postgres,Prod-01,2,2,2,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_order_event,TRUE,postgres,Prod-01,2,2,1,TRUE,60,id,install_ts,install_ts,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_order_feedback,FALSE,postgres,Prod-01,2,2,2,TRUE,180,id,,install_ts,2.1.2,S089D0GQRCJ,180,,,FALSE,2,,
oms_bifrost,public,oms_order_item_cancellation,TRUE,postgres,Prod-01,2,2,1,TRUE,60,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_order_item,TRUE,postgres,Prod-01,1,1,1,TRUE,60,id,install_ts,install_ts,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_order,TRUE,postgres,Prod-01,2,2,3,TRUE,60,id,install_ts,install_ts,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_procurement_update_item,TRUE,postgres,Prod-01,1,1,2,TRUE,180,id,install_ts,install_ts,2.1.2,S089D0GQRCJ,180,,,FALSE,2,,
oms_bifrost,public,oms_procurement_update,TRUE,postgres,Prod-01,2,2,2,TRUE,180,id,install_ts,install_ts,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_reason,TRUE,postgres,Prod-01,2,2,2,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_refund,FALSE,postgres,Prod-01,2,6,2,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_self_service_action,FALSE,postgres,Prod-01,2,6,2,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_sort_criteria,FALSE,postgres,Prod-01,2,2,2,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_source,FALSE,postgres,Prod-01,2,6,2,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_suborder_event,TRUE,postgres,Prod-01,2,1,1,TRUE,60,id,install_ts,install_ts,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_suborder_item,TRUE,postgres,Prod-01,1,2,1,TRUE,60,id,install_ts,install_ts,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_suborder,TRUE,postgres,Prod-01,2,2,3,TRUE,60,id,install_ts,install_ts,2.1.2,S089D0GQRCJ,180,,,FALSE,1,,
oms_bifrost,public,oms_cart_event,TRUE,postgres,Prod-01,5,3,3,TRUE,60,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_oms_bifrost,FALSE,1,,
