Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
lms,public,courses,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_lms,FALSE,1,,
lms,public,tenants,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_lms,FALSE,1,,
lms,public,course_level_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_lms,FALSE,1,,
lms,public,course_tenant_mappings,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_lms,FALSE,1,,
lms,public,user_course_sessions,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_lms,FALSE,1,,
lms,public,task_dataset_mappings,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_lms,FALSE,1,,
lms,public,user_level_ledger,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_lms,FALSE,1,,
lms,public,tasks,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_lms,FALSE,1,,
