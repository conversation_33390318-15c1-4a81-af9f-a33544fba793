Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
search_ranking,public,knowledge_base,FALSE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,keyterm_ptype_mappings,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,search_spellcorrect_map,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,keyterm_product_id_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,search_keyterms,TRUE,postgres,Prod-01,1,1,1,TRUE,180,keyterm_id,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,sr_keyword_count,TRUE,postgres,Prod-01,1,1,1,TRUE,180,entity_name,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,saleability_city_level_details_v1,TRUE,postgres,Prod-01,1,1,1,TRUE,180,"keyword,city_id,product_id",,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,saleability_pan_india_map_v1,TRUE,postgres,Prod-01,1,1,1,TRUE,180,"keyword,product_id",,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,search_similarity,FALSE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,usecase_item,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,2,,
search_ranking,public,usecase,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,2,,
search_ranking,public,search_atc_data,TRUE,postgres,Prod-01,1,1,1,TRUE,180,"keyword,product_id,city_id,entity_type",,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,transliteration,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,usecase_tag,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,usecase_tag_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,usecase_item_section_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,usecase_item_data,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,usecase_item_section,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,container,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,component,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,entity_container_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,container_component_mapping,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,dump_products,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,autosuggest_entity_ranking,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,newly_launched_products,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,entity_city_metrics,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
search_ranking,public,city_info,TRUE,postgres,Prod-01,1,1,1,TRUE,180,city_id,,,2.1.2,S089D0GQRCJ,180,,lake_search_ranking,FALSE,1,,
