Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
warehouse_metrics,public,reverse_trip,FALSE,postgres,Prod-01,1,1,1,TRUE,180,trip_id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_warehouse_metrics,FALSE,1,,
warehouse_metrics,public,outbound_demand,TRUE,postgres,Prod-01,1,1,1,TRUE,180,demand_id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_warehouse_metrics,FALSE,1,,
warehouse_metrics,public,employee_activity_tracker,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,2.1.2,S089D0GQRCJ,180,,lake_warehouse_metrics,FALSE,1,,
warehouse_metrics,public,report_registry,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,created_at,,,180,,lake_warehouse_metrics,FALSE,1,,
