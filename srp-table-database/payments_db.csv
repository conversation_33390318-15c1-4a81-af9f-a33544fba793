Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
payments_db,public,gr_payment,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_payments_db,FALSE,1,,
payments_db,public,gr_payment_refund,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,,2.1.2,S089D0GQRCJ,180,,lake_payments_db,FALSE,1,,
