Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
invoicing,public,customer_invoice,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_invoicing,FALSE,1,,
invoicing,public,customer_invoice_service_charges,TRUE,postgres,Prod-01,1,1,1,TRUE,180,id,,install_ts,2.1.2,S089D0GQRCJ,180,,lake_invoicing,FALSE,1,,
