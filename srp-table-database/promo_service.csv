Database,Schema,Table,SRP Ready,DB Type,Cluster Type,Source Connector Group,Sink Connector Group,Dagger Group,Cluster Reuse,SLA mins,Primary Key,Sort Key,Partition Key,<PERSON><PERSON><PERSON>,Owner Id,Dagger Poke Interval,Dagger Task Concurrency,Dagger Lake Schema,Redshift Replication,Table Version,PII Columns,Transformations
promo_service,promo_service,promos,TRUE,mysql,Prod-01,1,1,1,TRUE,180,promo_id,,,2.1.2,S089D0GQRCJ,180,,lake_promo_service,FALSE,1,,
promo_service,promo_service,salts,TRUE,mysql,Prod-01,1,1,1,TRUE,180,salt_id,,,2.1.2,S089D0GQRCJ,180,,lake_promo_service,FALSE,1,,
promo_service,promo_service,promo_codes,TRUE,mysql,Prod-01,1,1,1,TRUE,180,code_id,,,2.1.2,S089D0GQRCJ,180,,lake_promo_service,FALSE,1,,
promo_service,promo_service,offers,TRUE,mysql,Prod-01,1,1,1,TRUE,180,offer_id,,,2.1.2,S089D0GQRCJ,180,,lake_promo_service,FALSE,1,,
promo_service,promo_service,conditions,TRUE,mysql,Prod-01,1,1,1,TRUE,180,condition_id,,,2.1.2,S089D0GQRCJ,180,,lake_promo_service,FALSE,1,,
promo_service,promo_service,expressions,TRUE,mysql,Prod-01,1,1,1,TRUE,180,expression_id,,,2.1.2,S089D0GQRCJ,180,,lake_promo_service,FALSE,1,,
promo_service,promo_service,campaigns,TRUE,mysql,Prod-01,1,1,1,TRUE,180,campaign_id,,,2.1.2,S089D0GQRCJ,180,,lake_promo_service,FALSE,1,,
promo_service,promo_service,campaign_history,TRUE,mysql,Prod-01,1,1,1,TRUE,180,id,,updated_at,2.1.2,S089D0GQRCJ,180,,lake_promo_service,FALSE,1,,
promo_service,promo_service,offer_budget,TRUE,mysql,Prod-01,1,1,1,TRUE,180,budget_id,,,2.1.2,S089D0GQRCJ,180,,lake_promo_service,FALSE,1,,
promo_service,promo_service,user,TRUE,mysql,Prod-01,1,1,1,TRUE,180,user_id,,,2.1.2,S089D0GQRCJ,180,,lake_promo_service,FALSE,1,,
promo_service,promo_service,campaign_conditions,TRUE,mysql,Prod-01,1,1,1,TRUE,180,condition_id,,,2.1.2,S089D0GQRCJ,180,,lake_promo_service,FALSE,1,,
