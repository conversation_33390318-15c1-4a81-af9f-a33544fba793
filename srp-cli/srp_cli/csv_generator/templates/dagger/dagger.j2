dag_name: {{dag_name}}
dag_type: etl
escalation_priority: low
alert_configs:
  slack:
    - channel: bl-data-alerts-p1
  error_channel_mapping:
    terminated_with_errors: bl-data-spot-interruptions
    user_request: bl-data-spot-interruptions
    spot: bl-data-spot-interruptions
executor:
  config:
    service_account_name: blinkit-prod-airflow-de-primary-eks-role
    cpu:
      limit: 1
      request: 0.3
    image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de:stable
    memory:
      limit: 500M
      request: 250M
    node_selectors:
      nodetype: airflow-srp-spot
    tolerations:
      - effect: NoSchedule
        key: service
        operator: Equal
        value: airflow-srp
    volume_mounts:
      - mountPath: /usr/local/airflow/plugins
        name: airflow-dags
        subPath: airflow-de/plugins
  type: celery
namespace: de
owner:
  email: <EMAIL>
  slack_id: {{ owner_id }}
path: de/replicate/etl/{{dag_name}}
paused: false
project_name: replicate
schedule:
  end_date: "{{ end_date }}"
  interval: {{ interval }}
  start_date: "{{ start_date }}"
schedule_type: fixed
sla: {{ sla_mins }} minutes
task_concurrency: {{ dagger_task_concurrency | int if dagger_task_concurrency }}
poke_interval: {{ dagger_poke_interval | int if dagger_poke_interval }}
redshift_sla_seconds: {{ sla_mins * 60 }}
emr_sensor: {}
spark:
  aws_conn_id: aws_default
  mode: emr
  nessie_jar_version: {{nessie_jar}}
  cluster_reuse: {{ cluster_reuse|lower }}
{%- if nessie_jar.split('.')[0]|int >= 2 %}
  emr_version: 6.11.1
{%- endif %}
support_files: []
tables:
{%- for t in tables %}
  - flow_id: {{t.flow_id}}
    topic_name: {{t.db_type}}.{{t.database}}.{{t.schema}}.{{t.table}}
    redshift_replication: {{t.redshift_replication}}
    sink:
      column_dtypes:
        {%- for c in t.columns %}
        - name: {{c.name}}
          type: {{c.dtype}}
        {%- endfor %}
      copy_params:
        {%- for param in t.copy_params %}
        - {{param}}
        {%- endfor %}
      incremental_key: {{t.primary_key}}
      force_upsert_without_increment_check: True
      load_type: upsert
      primary_key: [{{t.primary_key}}]
      s3_url: s3://prod-dse-nessie-output/{{t.db_type}}.{{t.database}}.{{t.schema}}.{{t.table}}/
      {%- if dagger_lake_schema is not none %}
      schema: {{ dagger_lake_schema }}
      {%- else %}
      schema: lake_{{ t.database }}
      {%- endif %}
      sortkey: [{{t.sort_key}}]
      table: {{t.table}}
    source:
      database: {{t.database}}
      table: {{t.table}}
  {%- endfor %}
tags:
  - de
  - replicate
  - {{dag_name}}
template_name: nessie
version: {{dag_version}}
