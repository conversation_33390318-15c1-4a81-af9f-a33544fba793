import click
import time
from pathlib import Path
import json
import os
from srp_cli.connect.connector import (
    get_connector_list,
    validate_connector,
    Connector,
    cluster_name_ip_mapping,
    add_connector)
from srp_cli.utilities.decorator import auth_decorator
from srp_cli.settings import MSK_BROKER_HISTORY_MAPPING, DEBEZIUM_RAW_BUCKET_MAPPING

BASE_VAULT_PATH = "data/services/source-replication/kafka-connect/databases"

vault_path_db_name_mapping = {
        "mother_db": f"{BASE_VAULT_PATH}/postgres/grofers_db_new1/grofers_db_reader_srp",
        "bifrost_db": f"{BASE_VAULT_PATH}/postgres/oms_bifrost/oms_bifrost_aurora_reader_srp_connect",
        "logex": f"{BASE_VAULT_PATH}/postgres/logex/logex_reader_srp_connect",
        "last_mile": f"{BASE_VAULT_PATH}/mysql/last_mile/reader_srp",
        "supply_orchestrator": f"{BASE_VAULT_PATH}/postgres/supply_orchestrator/supply_orchestrator_reader_srp_connect",
        "po_db": f"{BASE_VAULT_PATH}/mysql/po/po_application_srp_aurora",
        "snorlax_db": f"{BASE_VAULT_PATH}/mysql/snorlax/snorlax_application_srp_aurora",
        "ims_db": f"{BASE_VAULT_PATH}/mysql/ims/ims_application_srp_aurora",
        "pos_db": f"{BASE_VAULT_PATH}/mysql/pos/pos_application_srp_aurora",
        "payments_db": f"{BASE_VAULT_PATH}/postgres/payments/payments_reader_srp_aurora",
        "payments_db_aurora": f"{BASE_VAULT_PATH}/postgres/payments/payments_reader_srp",
        "cart_db": f"{BASE_VAULT_PATH}/postgres/cart/cart_reader_srp_connect_aurora",
        "user_db": f"{BASE_VAULT_PATH}/postgres/user/user_application_srp",
        "promotion_db": f"{BASE_VAULT_PATH}/postgres/promotion/promotion_reader_srp_connect_aurora",
        "ars_db": f"{BASE_VAULT_PATH}/mysql/ars/ars_application_srp_aurora",
        "crates_db": f"{BASE_VAULT_PATH}/mysql/crates/crates_application_srp_aurora",
        "reports_db": f"{BASE_VAULT_PATH}/mysql/reports/reports_application_srp_aurora",
        "finance_db": f"{BASE_VAULT_PATH}/postgres/finance_db/finance_db_reader_srp_connect_aurora",
        "crm_db": f"{BASE_VAULT_PATH}/postgres/crm/crm_reader_srp_connect_aurora",
        "cms_historian_db": f"{BASE_VAULT_PATH}/postgres/cms_historian/cms_historian_reader_srp_connect_aurora",
        "logistics_server_db": f"{BASE_VAULT_PATH}/postgres/logistics_server/logistics_server_reader_srp_connect_aurora",
        "pricing_v3_db": f"{BASE_VAULT_PATH}/postgres/pricing_v3/pricing_v3_reader_srp_connect_aurora",
        "ticktock_db": f"{BASE_VAULT_PATH}/postgres/ticktock/ticktock_reader_srp_connect",
        "warehouse_location_db": f"{BASE_VAULT_PATH}/mysql/warehouse_location/whl_application_srp_aurora",
        "vms_db": f"{BASE_VAULT_PATH}/mysql/vms/vms_application_srp_aurora",
        "offer_master_db": f"{BASE_VAULT_PATH}/mysql/offer_master/offer_master_application_srp_aurora",
        "retail_db": f"{BASE_VAULT_PATH}/mysql/retail/retail_application_srp_aurora",
        "rpc_db": f"{BASE_VAULT_PATH}/mysql/rpc/rpc_application_srp_aurora",
        "adserver_db": f"{BASE_VAULT_PATH}/postgres/adserver/adserver_application_srp_aurora",
        "anomalies_db": f"{BASE_VAULT_PATH}/mysql/anomalies/anomalies_application_srp_aurora",
        "fleet_management_db": f"{BASE_VAULT_PATH}/postgres/fleet_management/fleet_management_reader_srp_connect_aurora",
        "search_ranking_db": f"{BASE_VAULT_PATH}/postgres/search_ranking/search_ranking_application_srp",
        "locations_geo_tracker_db": f"{BASE_VAULT_PATH}/postgres/locations_geo_tracker/locations_geo_tracker_application_srp_aurora",
        "auth_db": f"{BASE_VAULT_PATH}/postgres/auth/auth_application_srp_aurora",
        "storeops_db": f"{BASE_VAULT_PATH}/postgres/storeops/storeops_application_srp_aurora",
        "serviceability_db": f"{BASE_VAULT_PATH}/postgres/serviceability/serviceability_application_srp_aurora",
        "payouts_engine_db": f"{BASE_VAULT_PATH}/postgres/payouts_engine/payouts_engine_application_srp_aurora",
        "vendor_console_db": f"{BASE_VAULT_PATH}/postgres/vendor_console/vendor_console_application_srp_aurora",
        "trade_finance_db": f"{BASE_VAULT_PATH}/postgres/trade_finance/trade_finance_application_srp_aurora",
        "cms_db": f"{BASE_VAULT_PATH}/postgres/cms/cms_reader_srp_connect",
        "ops_management_db": f"{BASE_VAULT_PATH}/postgres/ops_management/ops_management_application_srp_aurora",
        "notify_me_db": f"{BASE_VAULT_PATH}/postgres/notify_me/notify_me_application_aurora",
        "merchant_dashboard_db": f"{BASE_VAULT_PATH}/postgres/merchant_dashboard/merchant_dashboard_srp_connect",
        "product_knowledge_metadata": f"{BASE_VAULT_PATH}/postgres/product_knowledge_metadata/product_knowledge_metadata_reader_srp_connect",
        "transit_server": f"{BASE_VAULT_PATH}/postgres/transit_server/transit_server_application_srp",
        "wms": f"{BASE_VAULT_PATH}/mysql/wms/wms_application_srp",
        "sauron": f"{BASE_VAULT_PATH}/postgres/sauron/sauron_application_srp",
        "supply_metrics": f"{BASE_VAULT_PATH}/postgres/supply_metrics/supply_metrics_application_srp",
        "promo_service": f"{BASE_VAULT_PATH}/mysql/promo_service/promo_service_application_srp",
        "document_digitisation": f"{BASE_VAULT_PATH}/postgres/document_digitisation/document_digitisation_application_srp_aurora_2",
        "serviceability_controltower": f"{BASE_VAULT_PATH}/postgres/serviceability_controltower/serviceability_controltower_application_srp_aurora",
        "game": f"{BASE_VAULT_PATH}/postgres/game/game_reader_srp_connect",
        "asset_tracking": f"{BASE_VAULT_PATH}/postgres/asset_tracking/asset_tracking_application_srp_aurora",
        "redash_reports": f"{BASE_VAULT_PATH}/postgres/redash_reports/redash_reports_srp_replication_aurora_2",
        "redash_queries": f"{BASE_VAULT_PATH}/postgres/redash_queries/redash_queries_srp_replication_aurora_2",
        "locus": f"{BASE_VAULT_PATH}/postgres/locus/locus_reader_srp_connect",
        "locus_v2": f"{BASE_VAULT_PATH}/postgres/locus_v2/locus_v2_reader_srp_connect",
        "dse_datakart": f"{BASE_VAULT_PATH}/postgres/dse_datakart/dse_datakart_reader_srp",
        "iot": f"{BASE_VAULT_PATH}/postgres/iot/iot_reader_srp_connect",
        "irs": f"{BASE_VAULT_PATH}/postgres/irs/irs_application_srp",
        "b2b_oms": f"{BASE_VAULT_PATH}/postgres/b2b_oms/b2b_oms_application_srp",
        "seller": f"{BASE_VAULT_PATH}/postgres/seller/seller_application_srp_aurora",
        "velocity": f"{BASE_VAULT_PATH}/postgres/velocity/velocity_application_srp",
        "dse_db": f"{BASE_VAULT_PATH}/postgres/dse_db/dse_db_application_srp_connect",
        "location": f"{BASE_VAULT_PATH}/postgres/location/location_reader_srp_connect",
        "pms": f"{BASE_VAULT_PATH}/postgres/pms/pms_application_srp",
        "user_management": f"{BASE_VAULT_PATH}/postgres/user_management/user_management_application_srp",
        "lms": f"{BASE_VAULT_PATH}/postgres/lms/lms_reader_srp",
        "kitchen": f"{BASE_VAULT_PATH}/postgres/kitchen/kitchen_application_srp",
        "invoicing": f"{BASE_VAULT_PATH}/postgres/invoicing/invoicing_application_srp",
        "EveProd": f"{BASE_VAULT_PATH}/sqlserver/eveprod/eveprod_application_srp",
        "elite_store_partner": f"{BASE_VAULT_PATH}/postgres/elite_store_partner/elite_store_partner_application_srp",
        "digital_asset_management": f"{BASE_VAULT_PATH}/postgres/digital_asset_management/digital_asset_management_reader_srp_connect",
        "supply_chain_network_controller": f"{BASE_VAULT_PATH}/postgres/supply_chain_network_controller/supply_chain_network_controller_application_srp",
        "warehouse_metrics": f"{BASE_VAULT_PATH}/postgres/warehouse_metrics/warehouse_metrics_application_srp",
        "b2b_oms_projections": f"{BASE_VAULT_PATH}/postgres/b2b_oms_projections/b2b_oms_projections_application_srp",
        "adserver_analytics": f"{BASE_VAULT_PATH}/postgres/adserver_analytics/adserver_analytics_application_srp",
        "logistics_insights": f"{BASE_VAULT_PATH}/postgres/logistics_insights/logistics_insights_application_srp",
        "emergency_services": f"{BASE_VAULT_PATH}/postgres/emergency_services/emergency_services_application_srp",
        "bulk_upload": f"{BASE_VAULT_PATH}/mysql/bulk_upload/bulk_upload_application_srp_2",
        "inventory_lm": f"{BASE_VAULT_PATH}/postgres/inventory_lm/inventory_lm_application_srp",
        "layout_service_preprod": f"{BASE_VAULT_PATH}/postgres/layout_service_preprod/layout_service_preprod_application_srp",
        "pricing_engine": f"{BASE_VAULT_PATH}/postgres/pricing_engine/pricing_engine_application_srp",
        "search_management": f"{BASE_VAULT_PATH}/postgres/search_management/search_management_reader_srp",
        "airflow": f"{BASE_VAULT_PATH}/postgres/airflow/airflow_application_srp",
    }
db_hostname_mapping = {
        "grofers_db_new1": "mother_db",
        "oms_bifrost": "bifrost_db",
        "ims": "ims_db",
        "pos": "pos_db",
        "po": "po_db",
        "logex": "logex",
        "last_mile": "last_mile",
        "supply_orchestrator": "supply_orchestrator",
        "snorlax": "snorlax_db",
        "payments": "payments_db",
        "payments_db": "payments_db_aurora",
        "cart": "cart_db",
        "user": "user_db",
        "promotion": "promotion_db",
        "ars": "ars_db",
        "crates": "crates_db",
        "reports": "reports_db",
        "finance_db": "finance_db",
        "crm": "crm_db",
        "cms_historian": "cms_historian_db",
        "logistics_server": "logistics_server_db",
        "pricing_v3": "pricing_v3_db",
        "ticktock": "ticktock_db",
        "warehouse_location": "warehouse_location_db",
        "vms": "vms_db",
        "offer_master": "offer_master_db",
        "retail": "retail_db",
        "rpc": "rpc_db",
        "adserver": "adserver_db",
        "anomalies": "anomalies_db",
        "fleet_management": "fleet_management_db",
        "search_ranking": "search_ranking_db",
        "locations_geo_tracker": "locations_geo_tracker_db",
        "auth": "auth_db",
        "storeops": "storeops_db",
        "serviceability": "serviceability_db",
        "payouts_engine": "payouts_engine_db",
        "vendor_console": "vendor_console_db",
        "trade_finance": "trade_finance_db",
        "cms": "cms_db",
        "ops_management": "ops_management_db",
        "notify_me": "notify_me_db",
        "merchant_dashboard": "merchant_dashboard_db",
        "product_knowledge_metadata": "product_knowledge_metadata",
        "transit_server": "transit_server",
        "wms": "wms",
        "sauron": "sauron",
        "supply_metrics": "supply_metrics",
        "promo_service": "promo_service",
        "document_digitisation": "document_digitisation",
        "serviceability_controltower" : "serviceability_controltower",
        "game": "game",
        "asset_tracking" : "asset_tracking",
        "redash_reports" : "redash_reports",
        "redash_queries" : "redash_queries",
        "locus": "locus",
        "locus_v2": "locus_v2",
        "dse_datakart" : "dse_datakart",
        "iot" : "iot",
        "irs": "irs",
        "seller": "seller",
        "velocity": "velocity",
        "b2b_oms": "b2b_oms",
        "dse_db": "dse_db",
        "location": "location",
        "pms": "pms",
        "user_management": "user_management",
        "lms": "lms",
        "kitchen": "kitchen",
        "invoicing": "invoicing",
        "EveProd": "EveProd",
        "elite_store_partner":  "elite_store_partner",
        "digital_asset_management": "digital_asset_management",
        "supply_chain_network_controller": "supply_chain_network_controller",
        "warehouse_metrics": "warehouse_metrics",
        "adserver_analytics": "adserver_analytics",
        "b2b_oms_projections": "b2b_oms_projections",
        "logistics_insights": "logistics_insights",
        "emergency_services": "emergency_services",
        "bulk_upload": "bulk_upload",
        "inventory_lm": "inventory_lm",
        "layout_service_preprod": "layout_service_preprod",
        "pricing_engine": "pricing_engine",
        "search_management": "search_management",
        "airflow": "airflow",
}

@click.group(name="connect-add", help="Utilities to add/update kafka connectors configs")
@click.option('--cluster_name', type=click.Choice(["supply", "test", "prep-01", "prod-01"]), help='Name of kafka connect cluster')
@click.option('--connector_type', type=click.Choice(["s3_sink", "mysqldb_kafka", "postgresdb_kafka", "sqlserverdb_kafka"]), help="Connect connector type")
@click.option('--config_file', type=click.File(), help='Path to config file for kafka connector')
@click.pass_context
@auth_decorator
def cli(ctx, cluster_name, connector_type, config_file):
    config_file_name = config_file.name.split('/')[-1]
    output_conf_file = json.loads(config_file.read())
    if connector_type in ("mysqldb_kafka", "postgresdb_kafka", "sqlserverdb_kafka"):
        output_conf_file["config"].update({
            "database.user": f"""${{vault:{vault_path_db_name_mapping[db_hostname_mapping[config_file_name.split('.')[1]]]}:user}}""",
            "database.password": f"""${{vault:{vault_path_db_name_mapping[db_hostname_mapping[config_file_name.split('.')[1]]]}:password}}""",
            "database.hostname": f"""${{vault:{vault_path_db_name_mapping[db_hostname_mapping[config_file_name.split('.')[1]]]}:host}}""",
            "database.port": f"""${{vault:{vault_path_db_name_mapping[db_hostname_mapping[config_file_name.split('.')[1]]]}:port}}"""
        })

    ## Quick hack to avoid issues when testing our connectors
    # For Postgres - adding a same name replication slot ( and dropping it ) in our test cluster
    # For MySQL - using the same history topic can cause issues
    if "test" in cluster_name or "prep" in cluster_name:
        if connector_type == "mysqldb_kafka":
            output_conf_file["config"].update({
                "database.history.kafka.bootstrap.servers": MSK_BROKER_HISTORY_MAPPING[cluster_name]
            })
        elif connector_type == "sqlserverdb_kafka":
            output_conf_file["config"].update({
                "database.history.kafka.bootstrap.servers": MSK_BROKER_HISTORY_MAPPING[cluster_name]
            })
        elif connector_type == "postgresdb_kafka":
            slot_name = output_conf_file["config"]["slot.name"]
            output_conf_file["config"].update({
                "slot.name" : f"t_{slot_name}"
            })
        elif connector_type == "s3_sink":
            output_conf_file["config"].update({
                "s3.bucket.name": DEBEZIUM_RAW_BUCKET_MAPPING[cluster_name]
            })

    print(json.dumps(output_conf_file["config"]))

    validate_res = validate_connector(cluster_name, output_conf_file)
    if validate_res.ok:
        if validate_res.json()["error_count"] > 0:
            ctx.obj["validation"] = False
            errors = []
            for config in validate_res.json()["configs"]:
                if config["value"]["errors"]:
                    errors.append(config)
            click.echo(f"Request to validate connector failed. Response was {errors}")
            return
    else:
        click.echo(f"Request to validate connector failed. Response was {validate_res.__dict__}")
        ctx.obj["validation"] = False
        return
    ctx.obj["validation"] = True
    ctx.obj["cluster_name"] = cluster_name
    ctx.obj["output_conf_file"] = output_conf_file


@cli.command()
@click.pass_context
def add(ctx):
    if(not ctx.obj["validation"]):
        click.echo("Request to add connector is unsuccessful due to config validation errors.")
        return
    time.sleep(10)
    res = add_connector(ctx.obj["cluster_name"],ctx.obj["output_conf_file"])
    if res.status_code == 201:
        click.echo("Request to add connector is successful")
    else:
        click.echo(f"Request to add connector failed. Response was {res.__dict__}")

@cli.command()
@click.pass_context
def update(ctx):
    if(not ctx.obj["validation"]):
        click.echo("Request to add connector is unsuccessful due to config validation errors.")
        return
    con = Connector(cluster_name_ip_mapping[ctx.obj["cluster_name"]],ctx.obj["output_conf_file"]["name"])
    res = con.add_or_update(ctx.obj["output_conf_file"]["config"])
    if res.status_code == 201:
        click.echo("Request to add connector is successful")
    elif res.status_code == 200:
        click.echo("Request to update connector is successful")
    else:
        click.echo(f"Request to add connector failed. Response was {res.__dict__}")
